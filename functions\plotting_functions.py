"""
Plotting utilities for exploratory analysis of unit firing rates and balloon events.

This module contains reusable plotting functions that were originally defined
inside exploratory_analysis_b24_b25.ipynb.

Conventions
- Time is in seconds.
- Balloon timing columns are sequences of [start, end] pairs per row.
"""
from __future__ import annotations

from pathlib import Path
from typing import Any, List, Mapping, MutableMapping, Optional, Sequence, Tuple, Dict
from datetime import datetime
import re

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt


from pathlib import Path
from datetime import datetime
import re
from typing import Optional

from pathlib import Path
from typing import Optional
from datetime import datetime
import re

def _generate_plot_filename(
    description: str,
    extension: str = "png",
    plots_dir: Optional[Path | str] = None,
    date: Optional[str] = None,
) -> Path:
    """Generate plots/YYYYMMDD/YYYYMMDD_description.ext with unique suffix if needed."""
    # resolve date
    date_str = str(date) if date is not None else datetime.now().strftime("%Y%m%d")

    # clean description
    cleaned_desc = re.sub(r"(_trials_\d+|_\d+)$", "", description or "")
    sanitized_desc = re.sub(r"[^\w\-.]", "_", cleaned_desc)
    sanitized_desc = re.sub(r"_+", "_", sanitized_desc).strip("_")

    # ensure extension like "png", not ".png"
    ext = extension.lstrip(".") if extension else "png"

    # build directory structure plots/{date}
    base_dir = Path(plots_dir) if plots_dir is not None else Path("plots")
    day_dir = base_dir / date_str
    day_dir.mkdir(parents=True, exist_ok=True)

    # base filename starts with date
    base_name = f"{date_str}" if not sanitized_desc else f"{date_str}_{sanitized_desc}"

    # unique filename handling
    counter = 0
    while True:
        suffix = "" if counter == 0 else f"_{counter:03d}"
        filename = f"{base_name}{suffix}.{ext}"
        full_path = day_dir / filename
        if not full_path.exists():
            return full_path
        counter += 1




def _paired_intervals_from_row(row: Mapping[str, Any], key: str = "balloon_on_sec") -> List[Tuple[float, float]]:
    """
    Extract a list of (start, end) float tuples from a DataFrame row for a given key.

    Parameters
    ----------
    row : Mapping[str, Any]
        Row-like object (e.g., a pandas Series) containing the intervals.
    key : str
        Column name that holds intervals as an array-like of shape (N, 2).

    Returns
    -------
    List[Tuple[float, float]]
        List of (start, end) pairs as floats. Returns an empty list if not present
        or in an unexpected format.
    """
    iv = row.get(key, None)
    if iv is None:
        return []
    arr = np.asarray(iv, dtype=float)
    if arr.ndim == 2 and arr.shape[1] == 2:
        return [(float(a), float(b)) for a, b in arr]
    return []


def _window_from_spikes(
    spike_times: Sequence[float],
    t_start: Optional[float] = None,
    t_end: Optional[float] = None,
    fallback: float = 1.0,
) -> Tuple[float, float]:
    """
    Infer a plotting window from spike timestamps if explicit t_start/t_end not provided.

    Parameters
    ----------
    spike_times : Sequence[float]
        Spike timestamps (s) for a unit.
    t_start, t_end : Optional[float]
        Optional bounds; if omitted, they are inferred from spike times.
    fallback : float
        If spikes are empty or t_end <= t_start, use this duration as a fallback.

    Returns
    -------
    (float, float)
        Start and end time for plotting.
    """
    st = np.asarray(spike_times, dtype=float).ravel()
    if st.size == 0:
        if t_start is None:
            t_start = 0.0
        if t_end is None:
            t_end = fallback
    else:
        if t_start is None:
            t_start = max(0.0, float(np.nanmin(st)))
        if t_end is None:
            t_end = float(np.nanmax(st))
        if t_end <= t_start:
            t_end = t_start + fallback
    return float(t_start), float(t_end)


def plot_each_unit_with_shading(
    df: pd.DataFrame,
    *,
    bin_size: float = 1.0,
    spike_key: str = "spike_times_sec",
    balloon_key: str = "balloon_on_sec",
    t_start: Optional[float] = None,
    t_end: Optional[float] = None,
    shade_alpha: float = 0.2,
    out_dir: Optional[Path | str] = None,
    dpi: int = 200,
    use_standard_filenames: bool = True,
    date: Optional[str] = None,
) -> List[str]:
    """
    For every row (unit) in `df`, plot that unit's firing rate (Hz) with shaded balloon intervals.

    Parameters
    ----------
    df : pd.DataFrame
        DataFrame with at least the columns specified by `spike_key` and `balloon_key`.
        Expected formats:
          - spike_key: array-like of spike times in seconds
          - balloon_key: array-like of shape (N, 2) with [start, end] pairs per row
    bin_size : float, default 1.0
        Histogram bin size in seconds.
    spike_key : str, default "spike_times_sec"
        DataFrame column name for spike time arrays.
    balloon_key : str, default "balloon_on_sec"
        DataFrame column name for balloon intervals; used for shading.
    t_start, t_end : Optional[float]
        Optional plotting window. If None, inferred per row from its spike times.
    shade_alpha : float, default 0.2
        Transparency for shaded balloon intervals.
    out_dir : Optional[Path | str]
        If provided, saves each figure to this directory instead of displaying on screen.
        If use_standard_filenames=True, this is ignored and plots are saved to /plots directory.
    dpi : int, default 200
        DPI when saving figures.
    use_standard_filenames : bool, default True
        If True, uses standardized YYYYMMDD_description.png filenames and saves to /plots directory.
        If False, uses legacy filename format in the specified out_dir.
    date : Optional[str], default None
        Date string in YYYYMMDD format for filename. If None, uses current date.

    Returns
    -------
    List[str]
        Paths of saved figures when `out_dir` is provided; otherwise an empty list.
    """
    saved_paths: List[str] = []
    save_dir: Optional[Path] = None
    if out_dir is not None:
        save_dir = Path(out_dir)
        save_dir.mkdir(parents=True, exist_ok=True)

    for idx, row in df.iterrows():
        # Window per unit (unless a fixed window is given)
        u_t_start, u_t_end = _window_from_spikes(
            row.get(spike_key, []), t_start=t_start, t_end=t_end, fallback=bin_size
        )

        # Common edges for this unit
        nbins = int(np.ceil((u_t_end - u_t_start) / bin_size))
        edges = u_t_start + np.arange(nbins + 1) * bin_size
        centers = (edges[:-1] + edges[1:]) / 2.0

        # Histogram for this unit
        st = np.asarray(row.get(spike_key, []), dtype=float).ravel()
        counts, _ = np.histogram(st, bins=edges)
        rate = counts.astype(float) / bin_size  # Hz

        # Plot
        fig, ax = plt.subplots(figsize=(12, 3.5))
        ax.step(centers, rate, where="mid", label="Firing rate (Hz)")
        ax.set_xlim(u_t_start, u_t_end)
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Firing rate (Hz)")

        # Title
        unit = row.get("unit", idx)
        animal = row.get("animal_id", "")
        ttl = f"Unit {unit}  |  Animal {animal}  |  bin={bin_size}s"
        ax.set_title(ttl)

        # Shade balloon intervals for this unit
        intervals = _paired_intervals_from_row(row, key=balloon_key)
        for on, off in intervals:
            if np.isfinite(on) and np.isfinite(off) and off > on:
                ax.axvspan(on, off, alpha=shade_alpha)

        ax.legend(loc="upper right")
        fig.tight_layout()

        if save_dir is not None or use_standard_filenames:
            if use_standard_filenames:
                # Use standardized filename generation
                animal = row.get("animal_id", "unknown")
                description = f"unit_{unit}_{animal}_bin{bin_size:.3f}s"
                fpath = _generate_plot_filename(description, extension='png', date=date)
            else:
                # Legacy filename format
                fname = f"unit_{unit}_bin{bin_size:.3f}s.png"
                fpath = save_dir / fname

            fig.savefig(fpath, dpi=dpi, bbox_inches="tight")
            plt.close(fig)
            saved_paths.append(str(fpath))

    return saved_paths



def _count_spikes_in_interval(spikes_sorted: np.ndarray, start: float, end: float) -> int:
    """Count spikes in half-open interval [start, end) using searchsorted."""
    li = np.searchsorted(spikes_sorted, start, side="left")
    ri = np.searchsorted(spikes_sorted, end, side="left")
    return int(max(0, ri - li))





def plot_units_grid_with_shading(
    df: pd.DataFrame,
    *,
    bin_size: float = 1.0,
    spike_key: str = "spike_times_sec",
    balloon_key: str = "balloon_on_sec",
    t_start: Optional[float] = None,
    t_end: Optional[float] = None,
    shade_alpha: float = 0.2,
    n_cols: int = 4,
    figsize_per_subplot: Tuple[float, float] = (4.0, 2.5),
    wspace: float = 0.25,
    hspace: float = 0.35,
    margins: Tuple[float, float, float, float] = (0.06, 0.98, 0.06, 0.06),  # left, right, bottom, top
    suptitle: Optional[str] = None,
    suptitle_y: Optional[float] = None,
    out_path: Optional[Path | str] = None,
    dpi: int = 200,
    file_format: Optional[str] = None,
    unit_class_map: Optional[Dict[Any, str]] = None,
    unit_col_name: str = "unit",
    show_legend: bool = True,
    use_standard_filenames: bool = True,
    date: Optional[str] = None,
    show_plot: bool = True,
) -> Optional[str]:


    """
    Create a single composite figure with subplots for each unit showing firing-rate histograms
    and shaded balloon intervals.

    Parameters
    ----------
    df : pd.DataFrame
        DataFrame with one row per unit and columns for `spike_key` and `balloon_key`.
    bin_size : float, default 1.0
        Histogram bin size in seconds.
    spike_key : str, default "spike_times_sec"
        Column name with arrays of spike timestamps (seconds).
    balloon_key : str, default "balloon_on_sec"
        Column name with intervals [[on, off], ...] for shading.
    t_start, t_end : Optional[float]
        Optional fixed plotting window for all units; if None, window is inferred per row.
    shade_alpha : float
        Alpha for shading balloon intervals.
    n_cols : int, default 4
        Number of columns in the subplot grid.
    figsize_per_subplot : (float, float)
        Size (width, height) in inches for each subplot; total size scales by rows/cols.
    wspace, hspace : float
        Subplot spacing parameters passed to subplots_adjust.
    margins : (left, right, bottom, top)
        Figure margins passed to subplots_adjust.
    suptitle : Optional[str]
        Optional figure-level title.
    out_path : Optional[Path | str]
        If provided, saves the composite figure here and returns the path. If None, returns None.
        If use_standard_filenames=True, this is used as a description for the standardized filename.
    dpi : int
        DPI used when saving the figure.
    file_format : Optional[str]
        Explicit image format to use when saving. If None, inferred from `out_path` extension.
    use_standard_filenames : bool, default True
        If True, uses standardized YYYYMMDD_description.png filenames and saves to /plots directory.
        If False, uses the provided out_path directly.
    date : Optional[str], default None
        Date string in YYYYMMDD format for filename. If None, uses current date.
    show_plot : bool, default True
        Whether to display the plot inline using plt.show(). When False, the plot
        is not displayed but can still be saved.

    Returns
    -------
    Optional[str]
        Saved image path if `out_path` is provided; otherwise None.
    """
    n_units = len(df)
    if n_units == 0:
        return None

    n_cols = max(1, int(n_cols))
    n_rows = int(np.ceil(n_units / n_cols))

    # Compute figure size
    sub_w, sub_h = figsize_per_subplot
    fig_w = sub_w * n_cols
    fig_h = sub_h * n_rows

    fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_w, fig_h), squeeze=False)
    axes_flat = axes.ravel()

    for ax in axes_flat:
        ax.set_visible(False)

    for idx, (row_idx, row) in enumerate(df.iterrows()):
        ax = axes_flat[idx]
        ax.set_visible(True)

        # Window per unit
        u_t_start, u_t_end = _window_from_spikes(
            row.get(spike_key, []), t_start=t_start, t_end=t_end, fallback=bin_size
        )
        nbins = int(np.ceil((u_t_end - u_t_start) / bin_size))
        edges = u_t_start + np.arange(nbins + 1) * bin_size
        centers = (edges[:-1] + edges[1:]) / 2.0

        st = np.asarray(row.get(spike_key, []), dtype=float).ravel()
        counts, _ = np.histogram(st, bins=edges)
        bin_widths = np.diff(edges)
        # Convert counts to firing rate (Hz) by dividing by bin width in seconds
        rate = counts.astype(float) / bin_widths

        # Plot histogram of firing rate (Hz)
        ax.step(centers, rate, where="mid", color="#2E2E2E")
        ax.set_xlim(u_t_start, u_t_end)

        # Axis ticks on all subplots; x-label only on bottom row
        if idx % n_cols == 0:
            ax.set_ylabel("FR (Hz)")
        ax.tick_params(axis="x", labelbottom=True)
        if idx // n_cols == n_rows - 1:
            ax.set_xlabel("Time (s)")
        else:
            ax.set_xlabel("")

        # Title with unit id
        unit = row.get(unit_col_name, row_idx)
        ax.set_title(f"Unit {unit}", fontsize=9)

        # Optional thick colored border based on unit class
        if unit_class_map is not None and unit in unit_class_map:
            cls = unit_class_map[unit]
            key = str(cls).lower()
            color = {
                "excited": "#1b9e77",                # teal/green
                "inhibited": "#d95f02",              # orange
                "non-responsive": "#808080",         # light grey
                "nonresponsive": "#808080",
                "mixed/non-responsive": "#808080",
                "mixed": "#808080",
            }.get(key, "black")
            # Set spine colors and linewidth
            for spine in ax.spines.values():
                spine.set_edgecolor(color)
                spine.set_linewidth(3.0)

        # Shade balloon intervals
        intervals = _paired_intervals_from_row(row, key=balloon_key)
        for on, off in intervals:
            if np.isfinite(on) and np.isfinite(off) and off > on:
                ax.axvspan(on, off, alpha=shade_alpha)

        # No legends per subplot

    # Spacing and margins
    left, right, bottom, top = margins
    fig.subplots_adjust(left=left, right=right, bottom=bottom, top=top, wspace=wspace, hspace=hspace)

    # Optional legend explaining border colors
    if show_legend and len(df) > 0:
        import matplotlib.patches as mpatches
        handles = [
            mpatches.Patch(edgecolor="#1b9e77", facecolor="none", linewidth=3, label="Excited"),
            mpatches.Patch(edgecolor="#d95f02", facecolor="none", linewidth=3, label="Inhibited"),
            mpatches.Patch(edgecolor="#808080", facecolor="none", linewidth=3, label="Mixed or non-responsive"),
        ]
        # Place the legend in figure space to avoid overlap; adjust anchor if needed
        fig.legend(handles=handles, loc="upper right", bbox_to_anchor=(0.985, 0.985), frameon=False)

    # Suptitle: simplify to recording name and move closer to plots
    title_text = None
    if suptitle is None:
        # Try to infer from df if a single recording is present
        if isinstance(df, pd.DataFrame) and ("animal_id" in df.columns):
            uniq = pd.unique(df["animal_id"].astype(str))
            if uniq.size == 1:
                title_text = str(uniq[0])
    else:
        # If user provided a title, simplify by removing trailing details after ':'
        title_text = str(suptitle).split(":", 1)[0].strip()

    if title_text:
        y = 0.96 if suptitle_y is None else suptitle_y
        fig.suptitle(title_text, y=y)

    saved_path: Optional[str] = None
    if out_path is not None:
        if use_standard_filenames:
            # Use out_path as description for standardized filename
            description = str(out_path).replace('.png', '').replace('.pdf', '').replace('.jpg', '')
            # Extract animal_id if available for better description
            if isinstance(df, pd.DataFrame) and "animal_id" in df.columns:
                uniq = pd.unique(df["animal_id"].astype(str))
                if uniq.size == 1:
                    description = f"{uniq[0]}_{description}"

            # Determine extension from file_format or default to png
            extension = file_format if file_format else 'png'
            final_path = _generate_plot_filename(description, extension=extension, date=date)
        else:
            # Use provided path directly (legacy behavior)
            final_path = Path(out_path)

        if file_format is None:
            fig.savefig(final_path, dpi=dpi, bbox_inches="tight")
        else:
            fig.savefig(final_path, dpi=dpi, bbox_inches="tight", format=file_format)
        saved_path = str(final_path)

    # Display or close the plot
    if show_plot:
        plt.show()
    else:
        plt.close(fig)

    return saved_path


def plot_mi_heatmap(
    df: pd.DataFrame,
    *,
    mi_col: str = "modulation_index",
    unit_col: str = "unit",
    vmax: Optional[float] = None,
    date: Optional[str] = None,
    use_standard_filenames: bool = True,
    show_plot: bool = True,
) -> Optional[str]:
    """
    Plot a heatmap of modulation indices for neural units.

    Heatmap of modulation index (trials × units), diverging colormap:
    blue < 0 < red, with 0 in white. NaNs shown in light gray.

    X-axis: Unit id (unit names from `unit_col`)
    Y-axis: trial1, trial2, ...

    Parameters
    ----------
    df : pd.DataFrame
        DataFrame containing neural data with unit and modulation_index columns.
    mi_col : str, default "modulation_index"
        Column name containing modulation indices (lists of values per unit).
    unit_col : str, default "unit"
        Column name containing unit identifiers.
    vmax : Optional[float], default None
        Maximum absolute value for colormap scaling. If None, uses data range.
    date : Optional[str], default None
        Date string in YYYYMMDD format for filename. If None, uses current date.
    use_standard_filenames : bool, default True
        If True, uses standardized YYYYMMDD_description.png filenames and saves to /plots directory.
    show_plot : bool, default True
        Whether to display the plot inline using plt.show(). When False, the plot
        is not displayed but can still be saved.

    Returns
    -------
    Optional[str]
        Path to saved plot if successful, None otherwise.
    """
    import matplotlib.pyplot as plt
    from matplotlib.colors import TwoSlopeNorm

    # Collect MI lists per unit
    mi_lists = df[mi_col].tolist()
    n_units = len(mi_lists)
    max_trials = max((len(m) for m in mi_lists), default=0)

    # Build matrix (units x trials), then transpose to (trials x units)
    M = np.full((n_units, max_trials), np.nan, dtype=float)
    for i, mis in enumerate(mi_lists):
        M[i, :len(mis)] = np.asarray(mis, dtype=float)
    M = M.T  # now shape = (max_trials, n_units): rows=trials, cols=units

    # Symmetric normalization around 0
    if vmax is None:
        finite = M[np.isfinite(M)]
        abs_max = np.max(np.abs(finite)) if finite.size else 1.0
    else:
        abs_max = float(vmax)
    norm = TwoSlopeNorm(vmin=-abs_max, vcenter=0.0, vmax=+abs_max)

    # Diverging colormap with white center; gray for NaN
    cmap = plt.get_cmap("bwr").copy()
    cmap.set_bad("0.85", alpha=1.0)  # NaNs as light gray

    # Figure size: wide for many units, tall for many trials
    fig_w = max(6, 0.35 * n_units)
    fig_h = max(4, 0.28 * max_trials)

    fig, ax = plt.subplots(figsize=(fig_w, fig_h))
    im = ax.imshow(M, aspect="auto", interpolation="nearest", cmap=cmap, norm=norm)

    # Colorbar
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label("Modulation index")

    # Tick labels
    # X: units
    units = df[unit_col].astype(str).tolist() if unit_col in df.columns else [str(i) for i in range(n_units)]
    ax.set_xticks(np.arange(n_units))
    ax.set_xticklabels(units, rotation=90, ha="center", va="top")

    # Y: trials as trial1, trial2, ...
    ax.set_yticks(np.arange(max_trials))
    ax.set_yticklabels([f"trial{i+1}" for i in range(max_trials)])
    ax.invert_yaxis()  # put trial1 at the top

    # Axis labels & title
    ax.set_xlabel("Unit id")
    ax.set_ylabel("(Trial #)")

    fig.tight_layout()

    # Save the plot
    saved_path = None
    if use_standard_filenames:
        plot_path = _generate_plot_filename('modulation_index_heatmap', date=date)
        fig.savefig(plot_path, dpi=200, bbox_inches='tight')
        saved_path = str(plot_path)

    # Display or close the plot
    if show_plot:
        plt.show()
    else:
        plt.close(fig)

    return saved_path


def plot_unit_classification_pie_chart_all(
    df: pd.DataFrame,
    *,
    figsize: Tuple[float, float] = (5, 5),
    class_order: List[str] = None,
    class_colors: Dict[str, str] = None,
    date: Optional[str] = None,
    use_standard_filenames: bool = True,
    show_plot: bool = True,
) -> Optional[str]:
    """
    Plot a pie chart showing unit classification across the entire dataset.

    Parameters
    ----------
    df : pd.DataFrame
        DataFrame containing neural data with 'unit_class' column.
    figsize : Tuple[float, float], default (5, 5)
        Figure size in inches (width, height).
    class_order : List[str], optional
        Order of classes for the pie chart. If None, uses default order.
    class_colors : Dict[str, str], optional
        Colors for each class. If None, uses default colors.
    date : Optional[str], default None
        Date string in YYYYMMDD format for filename. If None, uses current date.
    use_standard_filenames : bool, default True
        If True, uses standardized YYYYMMDD_description.png filenames and saves to /plots directory.
    show_plot : bool, default True
        Whether to display the plot inline using plt.show(). When False, the plot
        is not displayed but can still be saved if use_standard_filenames=True.

    Returns
    -------
    Optional[str]
        Path to saved plot if successful, None otherwise.
    """
    import matplotlib.pyplot as plt

    # Default class order and colors
    if class_order is None:
        class_order = ['excited', 'inhibited', 'mixed/non-responsive']

    if class_colors is None:
        class_colors = {
            'excited': '#1b9e77',
            'inhibited': '#d95f02',
            'mixed/non-responsive': '#808080'
        }

    # Calculate counts across the whole dataset
    counts = df['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)
    total = counts.sum()

    # Create labels with n values relative to total
    labels = [f'{cls} (n={counts[cls]}/{total})' for cls in class_order]
    colors = [class_colors[c] for c in class_order]

    # Create the plot
    fig, ax = plt.subplots(figsize=figsize)
    ax.pie(
        counts.values,
        labels=labels,
        colors=colors,
        autopct=lambda p: f'{p:.1f}%' if p > 0 else '',
        startangle=90,
        counterclock=False,
        wedgeprops=dict(linewidth=1, edgecolor='white')
    )
    ax.set_title(f'Response classification (total n={total})')
    ax.axis('equal')

    # Save the plot
    saved_path = None
    if use_standard_filenames:
        plot_path = _generate_plot_filename('unit_classification_pie_chart', date=date)
        fig.savefig(plot_path, dpi=200, bbox_inches='tight')
        saved_path = str(plot_path)

    # Display or close the plot
    if show_plot:
        plt.show()
    else:
        plt.close(fig)

    return saved_path


def plot_unit_classification_pie_chart_per_recording(
    df: pd.DataFrame,
    *,
    figsize: Tuple[float, float] = (16, 4),
    class_order: List[str] = None,
    class_colors: Dict[str, str] = None,
    animals: List[str] = None,
    date: Optional[str] = None,
    use_standard_filenames: bool = True,
    show_plot: bool = True,
) -> Optional[str]:
    """
    Plot pie charts showing unit classification for each recording/animal.

    Parameters
    ----------
    df : pd.DataFrame
        DataFrame containing neural data with 'unit_class' and 'animal_id' columns.
    figsize : Tuple[float, float], default (16, 4)
        Figure size in inches (width, height).
    class_order : List[str], optional
        Order of classes for the pie charts. If None, uses default order.
    class_colors : Dict[str, str], optional
        Colors for each class. If None, uses default colors.
    animals : List[str], optional
        List of animal IDs to plot. If None, uses default animals (animal_id column).
    date : Optional[str], default None
        Date string in YYYYMMDD format for filename. If None, uses current date.
    use_standard_filenames : bool, default True
        If True, uses standardized YYYYMMDD_description.png filenames and saves to /plots directory.
    show_plot : bool, default True
        Whether to display the plot inline using plt.show(). When False, the plot
        is not displayed but can still be saved if use_standard_filenames=True.


    Returns
    -------
    Optional[str]
        Path to saved plot if successful, None otherwise.
    """
    import matplotlib.pyplot as plt

    # Default parameters
    if class_order is None:
        class_order = ['excited', 'inhibited', 'mixed/non-responsive']

    if class_colors is None:
        class_colors = {
            'excited': '#1b9e77',
            'inhibited': '#d95f02',
            'mixed/non-responsive': '#808080'
        }

    if animals is None:
        animals = df['animal_id'].unique().tolist()

    # Group data by animal
    subsets = {k: v.copy() for k, v in df.groupby('animal_id')}

    # Create subplots
    fig, axes = plt.subplots(1, len(animals), figsize=figsize)
    if len(animals) == 1:
        axes = [axes]  # Ensure axes is always a list
    else:
        axes = axes.flatten()

    for ax, animal in zip(axes, animals):
        df_sub = subsets.get(animal, None)
        if df_sub is None or df_sub.empty:
            ax.axis('off')
            ax.set_title(f'{animal} (no data)', pad=2)
            continue

        counts = df_sub['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)
        total = int(counts.sum())

        # Labels for legend
        labels = [f'{cls} (n={counts[cls]}/{total})' for cls in class_order]
        colors = [class_colors[c] for c in class_order]

        wedges, texts, autotexts = ax.pie(
            counts.values,
            colors=colors,
            autopct=lambda p: f'{p:.1f}%' if p > 0 else '',
            startangle=90,
            counterclock=False,
            wedgeprops=dict(linewidth=1, edgecolor='white'),
            textprops={'fontsize': 8}  # smaller %
        )

        # Legend outside
        ax.legend(
            wedges, labels,
            loc='upper center',
            bbox_to_anchor=(0.5, -0.05),
            fontsize=7,  # smaller font
            ncol=1,
            frameon=False
        )

        ax.set_title(f'{animal} (n={total} units)', pad=2)
        ax.axis('equal')

    fig.tight_layout()

    # Save the plot
    saved_path = None
    if use_standard_filenames:
        plot_path = _generate_plot_filename('unit_classification_by_recording', date=date)
        fig.savefig(plot_path, dpi=200, bbox_inches='tight')
        saved_path = str(plot_path)

    # Display or close the plot
    if show_plot:
        plt.show()
    else:
        plt.close(fig)

    return saved_path


def _get_time_vec(tv):
    """Helper function to handle different time vector formats."""
    if tv is None:
        return lambda i: None
    if isinstance(tv, np.ndarray) and tv.ndim == 1 and tv.dtype != object:
        return lambda i: tv  # shared
    if hasattr(tv, "iloc"):  # pandas Series
        return lambda i: tv.iloc[i]
    return lambda i: tv[i]  # list / object array


def _series_to_list(x):
    """Helper function to convert series to list."""
    return x.tolist() if hasattr(x, "tolist") else list(x)


def plot_recording_waveforms(
    rec_name: str,
    rec_data,
    max_cols: int = 8,
    figsize_per_subplot: tuple = (1.5, 1.8),
    save: bool = True,
    outdir: str = ".",
    date: Optional[str] = None,
    use_standard_filenames: bool = True,
    show_plot: bool = True
):
    """Plot mean waveforms for all units in a recording.

    Creates a grid of subplots showing the mean waveform for each unit in the recording.
    Includes a 1ms scale bar in the last subplot for reference.

    Parameters
    ----------
    rec_name : str
        Name of the recording (used in plot title and filename)
    rec_data : DataFrame or dict
        Recording data containing waveform information. Can be:
        - DataFrame slice with columns: 'waveform_mean', 'unit', 'waveform_time_vector_ms'
        - Dictionary with keys: 'waveform_mean', 'unit', 'waveform_time_vector_ms'
    max_cols : int, default=8
        Maximum number of columns in the subplot grid
    figsize_per_subplot : tuple, default=(1.5, 1.8)
        Size of each individual subplot (width, height)
    save : bool, default=True
        Whether to save the plot to file
    outdir : str, default="."
        Output directory for saved plots (used only when use_standard_filenames=False)
    date : str, optional
        Date string for filename generation (YYYYMMDD format).
        If None, current date will be used when use_standard_filenames=True
    use_standard_filenames : bool, default=True
        Whether to use standardized filename generation via _generate_plot_filename()
    show_plot : bool, default=True
        Whether to display the plot inline using plt.show(). When False, the plot
        is not displayed but can still be saved if save=True.

    Returns
    -------
    None
        Displays the plot (if show_plot=True) and optionally saves it to file

    Notes
    -----
    - Handles both DataFrame slices and dictionary data formats
    - Automatically calculates optimal grid layout based on number of units
    - Adds a 1ms scale bar to the last subplot for reference
    - Uses black color for all waveforms for consistency
    - Removes axis ticks and labels for cleaner appearance
    """
    import math

    # Accept DataFrame slice or dict
    if hasattr(rec_data, "__getitem__") and not isinstance(rec_data, dict):
        if len(rec_data) == 0:
            print(f"[skip] {rec_name}: no units found.")
            return
        wf = rec_data["waveform_mean"]
        waveforms = _series_to_list(wf)
        units = _series_to_list(rec_data["unit"]) if "unit" in rec_data else None
        tv_col = rec_data["waveform_time_vector_ms"] if "waveform_time_vector_ms" in rec_data else None
    else:
        if rec_data is None:
            print(f"[skip] {rec_name}: data is None.")
            return
        wf = rec_data.get("waveform_mean", [])
        if isinstance(wf, np.ndarray) and wf.ndim == 2:
            waveforms = [wf[i, :] for i in range(wf.shape[0])]
        else:
            waveforms = list(wf)
        units = rec_data.get("unit", None)
        tv_col = rec_data.get("waveform_time_vector_ms", None)

    n_units = len(waveforms)
    if n_units == 0:
        print(f"[skip] {rec_name}: no units found.")
        return

    tv_get = _get_time_vec(tv_col)

    # Grid
    ncols = min(max_cols, max(1, int(math.ceil(math.sqrt(n_units)))))
    nrows = int(math.ceil(n_units / ncols))

    fig_w = figsize_per_subplot[0] * ncols
    fig_h = figsize_per_subplot[1] * nrows
    fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(fig_w, fig_h))

    if not isinstance(axes, np.ndarray):
        axes = np.array([axes])
    axes = axes.reshape(nrows, ncols)

    for i, ax in enumerate(axes.flat):
        if i < n_units:
            y = np.asarray(waveforms[i]).ravel()
            x = tv_get(i)
            if x is None:
                x = np.arange(len(y))
            ax.plot(x, y, color="black")
            if units is not None and i < len(units):
                ax.set_title(f"Unit {units[i]}", fontsize=7)
            ax.set_xticks([]); ax.set_yticks([])
            ax.axis("off")
        else:
            ax.axis("off")

    # ---- Add 1 ms scalebar INSIDE the last subplot without changing limits ----
    last_ax = axes.flat[min(n_units, len(axes.flat)) - 1]
    # Save current limits (so the scalebar doesn't shrink the waveform)
    xlim = last_ax.get_xlim()
    ylim = last_ax.get_ylim()

    xspan = xlim[1] - xlim[0]
    yspan = ylim[1] - ylim[0]

    # Desired bar length (1 ms in data units)
    bar_len = 1.0  # ms
    # If the axis window is <1 ms, fall back to 50% of the span
    if bar_len > xspan:
        bar_len = 0.5 * xspan

    # Place near bottom-right with a small margin
    x_margin = 0.05 * xspan
    y_margin = 0.08 * yspan
    x_end = xlim[1] - x_margin
    x_start = x_end - bar_len
    y_bar = ylim[0] + y_margin

    # Draw the bar + label, then restore limits so autoscale doesn't change
    last_ax.plot([x_start, x_end], [y_bar, y_bar], color="black", lw=2, solid_capstyle="butt", clip_on=False, zorder=5)
    last_ax.text((x_start + x_end) / 2, y_bar, "1 ms", ha="center", va="bottom", fontsize=7)

    last_ax.set_xlim(xlim)
    last_ax.set_ylim(ylim)
    # --------------------------------------------------------------------------

    fig.suptitle(f"Recording: {rec_name} — mean waveforms ({n_units} units)", fontsize=10)
    fig.tight_layout(rect=[0, 0, 1, 0.94])

    if save:
        if use_standard_filenames:
            plot_path = _generate_plot_filename(f'waveforms_{rec_name}', date=date)
        else:
            plot_path = Path(outdir) / f'waveforms_{rec_name}.png'
        plt.savefig(plot_path, dpi=200)

    if show_plot:
        plt.show()


__all__ = [
    "_generate_plot_filename",
    "plot_each_unit_with_shading",
    "plot_units_grid_with_shading",
    "plot_mi_heatmap",
    "plot_unit_classification_pie_chart_all",
    "plot_unit_classification_pie_chart_per_recording",
    "plot_recording_waveforms",
]
