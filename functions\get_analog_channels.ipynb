{"cells": [{"cell_type": "code", "execution_count": 2, "id": "9464bc3e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing: z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r1_g0\\d01_p1_r1_g0_t0.obx0.obx.bin\n", "Saved channel 0 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r1_g0\\channel_0.mat\n", "Saved channel 1 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r1_g0\\channel_1.mat\n", "Saved channel 2 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r1_g0\\channel_2.mat\n", "Saved channel 3 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r1_g0\\channel_3.mat\n", "Saved channel 4 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r1_g0\\channel_4.mat\n", "Saved channel 5 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r1_g0\\channel_5.mat\n", "Processing: z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r2_g0\\d01_p1_r2_g0_t0.obx0.obx.bin\n", "Saved channel 0 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r2_g0\\channel_0.mat\n", "Saved channel 1 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r2_g0\\channel_1.mat\n", "Saved channel 2 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r2_g0\\channel_2.mat\n", "Saved channel 3 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r2_g0\\channel_3.mat\n", "Saved channel 4 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r2_g0\\channel_4.mat\n", "Saved channel 5 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r2_g0\\channel_5.mat\n", "No .bin file found in z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r3_combined\n", "Processing: z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r4_g0\\d01_p1_r4_g0_t0.obx0.obx.bin\n", "Saved channel 0 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r4_g0\\channel_0.mat\n", "Saved channel 1 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r4_g0\\channel_1.mat\n", "Saved channel 2 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r4_g0\\channel_2.mat\n", "Saved channel 3 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r4_g0\\channel_3.mat\n", "Saved channel 4 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r4_g0\\channel_4.mat\n", "Saved channel 5 to z:\\users\\izouridis\\projects\\co2_nts\\data\\d01\\d01_p1_r4_g0\\channel_5.mat\n"]}], "source": ["import spikeinterface.extractors as se\n", "import numpy as np\n", "from scipy.io import savemat\n", "import os\n", "import glob\n", "import hdf5storage\n", "\n", "\n", "# --- Settings ---\n", "root_dir = r\"z:\\users\\izouridis\\projects\\co2_nts\\data\"\n", "# parent_folders = ['b12', 'b14', 'b15', 'b16', 'b17', 'b18']\n", "parent_folders = ['d01']\n", "sampling_frequency = 30303\n", "num_channels = 14\n", "dtype = 'int16'\n", "\n", "# --- Process each parent folder ---\n", "for parent in parent_folders:\n", "    parent_path = os.path.join(root_dir, parent)\n", "\n", "    # List first-level subfolders only\n", "    first_level_subfolders = [\n", "        os.path.join(parent_path, name)\n", "        for name in os.listdir(parent_path)\n", "        if os.path.isdir(os.path.join(parent_path, name))\n", "    ]\n", "\n", "    for subfolder_path in first_level_subfolders:\n", "        # Find .bin file inside this subfolder\n", "        bin_files = glob.glob(os.path.join(subfolder_path, '*.bin'))\n", "        if not bin_files:\n", "            print(f\"No .bin file found in {subfolder_path}\")\n", "            continue\n", "\n", "        binary_file = bin_files[0]\n", "        print(f\"Processing: {binary_file}\")\n", "\n", "        # Load recording\n", "        recording = se.BinaryRecordingExtractor(\n", "            file_paths=[binary_file],\n", "            sampling_frequency=sampling_frequency,\n", "            num_channels=num_channels,\n", "            dtype=dtype,\n", "            time_axis=0\n", "        )\n", "\n", "        # Extract metadata\n", "        fs = recording.get_sampling_frequency()\n", "        num_frames = recording.get_num_frames()\n", "        time_axis = np.arange(num_frames) / fs\n", "\n", "        # Define output directory (same as .bin location)\n", "        output_dir = os.path.dirname(binary_file)\n", "        os.makedirs(output_dir, exist_ok=True)\n", "\n", "        # Extract and save channels 0, 1, 2\n", "        for ch in [0, 1, 2, 3, 4, 5]:\n", "            recording_ch = recording.channel_slice(channel_ids=[ch])\n", "            trace = recording_ch.get_traces()[:, 0]\n", "            out_path = os.path.join(output_dir, f\"channel_{ch}.mat\")            \n", " \n", "            hdf5storage.write(\n", "                {\n", "                    f\"channel_{ch}\": trace,\n", "                    \"fs\": fs,\n", "                    \"time_axis\": time_axis\n", "                },\n", "                path='.', \n", "                filename=out_path,\n", "                matlab_compatible=True,\n", "                format='7.3',\n", "                truncate_existing=True\n", "            )\n", "\n", "            print(f\"Saved channel {ch} to {out_path}\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}