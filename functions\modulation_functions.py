"""
Reusable modulation analysis functions.

This module provides utilities to compute modulation indices (MI) from
neural spike times and balloon stimulation intervals, including a helper
for generating per-trial null MI distributions.

Conventions
- Time windows are half-open intervals: [start, end); a spike exactly at
  the end time is excluded.
- MI formula default: (on_rate - off_rate) / (on_rate + off_rate)

Author: Refactored from modulation_analysis_b24_b25.ipynb
"""
from __future__ import annotations

from typing import Tuple, Sequence, Union, Dict, Any, Optional, List
import numpy as np

def classify_unit(modulated_trial):
    n = len(modulated_trial)
    n_pos = sum(x == 1 for x in modulated_trial)
    n_neg = sum(x == -1 for x in modulated_trial)

    if n_pos >= n / 2 and n_neg == 0:
        return "excited"
    elif n_neg >= n / 2 and n_pos == 0:
        return "inhibited"
    else:
        return "mixed/non-responsive"

def modulation_index_single_trial(
    spike_times_sec: Sequence[float],
    on_range: Tuple[float, float],   # absolute times: [start, end)
    off_range: Tuple[float, float],  # absolute times: [start, end)
    mi_formula: str = "difference_over_sum",  # or "log_ratio"
    return_details: bool = False,
) -> Union[float, Tuple[float, Dict[str, Any]]]:
    """
    Compute the modulation index for a single trial given one ON window and one OFF window.

    Windows are half-open: [start, end). Spikes exactly at 'end' are excluded.

    Parameters
    ----------
    spike_times_sec : sequence of float
        Spike timestamps (s) for this trial.
    on_range, off_range : (start_s, end_s)
        Absolute window boundaries inside the trial.
    mi_formula : {"difference_over_sum", "log_ratio"}
        Formula used to compute the modulation index.
    return_details : bool
        If True, also return a dict with counts, rates, and window lengths.

    Returns
    -------
    float or (float, dict)
        MI value. NaN if a window length is zero or (for difference_over_sum) on+off==0.
        If return_details=True, also returns a details dict.
    """
    spikes_sorted = np.sort(np.asarray(spike_times_sec, dtype=float))

    on_start, on_end = map(float, on_range)
    off_start, off_end = map(float, off_range)
    if on_end < on_start:
        raise ValueError("on_range end must be >= start")
    if off_end < off_start:
        raise ValueError("off_range end must be >= start")

    on_len = on_end - on_start
    off_len = off_end - off_start
    if on_len == 0 or off_len == 0:
        return (float("nan"), {}) if return_details else float("nan")

    # half-open counts with searchsorted
    def _count(start: float, end: float) -> int:
        left_idx = np.searchsorted(spikes_sorted, start, side="left")
        right_idx = np.searchsorted(spikes_sorted, end, side="left")
        return int(right_idx - left_idx)

    on_count = _count(on_start, on_end)
    off_count = _count(off_start, off_end)

    rate_on = on_count / on_len
    rate_off = off_count / off_len

    if mi_formula == "difference_over_sum":
        denom = rate_on + rate_off
        mi = (rate_on - rate_off) / denom if denom != 0 else float("nan")
    elif mi_formula == "log_ratio":
        eps = 1e-12
        mi = float(np.log((rate_on + eps) / (rate_off + eps)))
    else:
        raise ValueError("mi_formula must be 'difference_over_sum' or 'log_ratio'")

    if return_details:
        details: Dict[str, Any] = {
            "on_count": on_count,
            "off_count": off_count,
            "rate_on": rate_on,
            "rate_off": rate_off,
            "on_len": on_len,
            "off_len": off_len,
        }
        return mi, details
    else:
        return mi


def compute_trialwise_mi(row: Dict[str, Any]) -> List[float]:
    """
    Compute observed per-trial MI for a row-like mapping.

    Expected keys in `row`:
      - 'spike_times_sec': 1D sequence of spike times for the unit
      - 'balloon_on_sec' : iterable of (start, end) ON windows per trial
      - 'balloon_off_sec': iterable of (start, end) OFF windows per trial

    Returns
    -------
    List[float]
        Observed MI values, one per paired ON/OFF trial.
    """
    spikes = row["spike_times_sec"]
    on_trials = row["balloon_on_sec"]
    off_trials = row["balloon_off_sec"]

    mi_values: List[float] = []
    for on_range, off_range in zip(on_trials, off_trials):
        mi = modulation_index_single_trial(
            spike_times_sec=spikes,
            on_range=tuple(on_range),
            off_range=tuple(off_range),
            mi_formula="difference_over_sum",
        )
        mi_values.append(mi)
    return mi_values


def compute_trialwise_mi_null(
    row: Dict[str, Any],
    *,
    window_len: float = 5.0,
    n_iter: int = 1000,
    mi_formula: str = "difference_over_sum",
    seed: Optional[int] = None,
) -> List[np.ndarray]:
    """
    For each OFF trial in the row, build a null MI distribution by:
      - Sampling `n_iter` random subwindows of length `window_len` within the OFF interval (as ON).
      - Comparing each to the WHOLE OFF interval (as OFF) using `modulation_index_single_trial`.

    Parameters
    ----------
    row : mapping-like
        Contains 'spike_times_sec' and 'balloon_off_sec'.
    window_len : float
        Length (s) of random subwindows drawn inside OFF to act as pseudo-ON windows.
    n_iter : int
        Number of random subwindows to sample per OFF trial.
    mi_formula : str
        MI formula passed through to `modulation_index_single_trial`.
    seed : Optional[int]
        Random seed; if provided, each trial uses seed + trial_idx for reproducibility.

    Returns
    -------
    List[np.ndarray]
        One array of MI values per OFF trial. Empty arrays for invalid trials.
    """
    spikes = row["spike_times_sec"]
    off_trials = row["balloon_off_sec"]

    per_trial_null: List[np.ndarray] = []
    for trial_idx, off_range in enumerate(off_trials):
        off_start, off_end = map(float, off_range)
        off_duration = off_end - off_start
        if off_duration <= 0 or off_duration < window_len:
            per_trial_null.append(np.array([], dtype=float))
            continue

        rng_trial = np.random.default_rng(None if seed is None else seed + trial_idx)
        starts = rng_trial.uniform(off_start, off_end - window_len, size=n_iter)

        mi_values = np.empty(n_iter, dtype=float)
        for i, start in enumerate(starts):
            mi_values[i] = modulation_index_single_trial(
                spike_times_sec=spikes,
                on_range=(start, start + window_len),  # random inside OFF
                off_range=(off_start, off_end),        # whole OFF
                mi_formula=mi_formula,
                return_details=False,
            )
        per_trial_null.append(mi_values)

    return per_trial_null




def modulation_flag(
    observed_mi: float,
    null_distribution: Sequence[float],
    *,
    lower_quantile: float = 0.05,
    upper_quantile: float = 0.95,
) -> int:
    """
    Classify modulation for a single observation against its null distribution.

    Returns
    -------
    int
        1  if observed_mi > upper_quantile percentile of null (enhanced)
       -1  if observed_mi < lower_quantile percentile of null (suppressed)
        0  otherwise

    Notes
    -----
    - Ignores NaNs in the null distribution.
    - Returns 0 if null is empty/NaN-only or observed_mi is not finite.
    - Uses strict inequalities (ties -> 0).
    """
    if not np.isfinite(observed_mi):
        return 0

    null = np.asarray(null_distribution, dtype=float)
    null = null[np.isfinite(null)]
    if null.size == 0:
        return 0

    lo = np.percentile(null, 100.0 * lower_quantile)
    hi = np.percentile(null, 100.0 * upper_quantile)

    if observed_mi > hi:
        return 1
    if observed_mi < lo:
        return -1
    return 0


def compute_trialwise_modulation_flags(
    row: Dict[str, Any],
    *,
    lower_quantile: float = 0.05,
    upper_quantile: float = 0.95,
) -> List[int]:
    """
    Apply modulation_flag trialwise for a mapping-like `row`.

    Expected keys in `row`:
      - row['modulation_index']      : list of observed MI per trial
      - row['modulation_index_null'] : list of null arrays per trial

    Parameters
    ----------
    lower_quantile, upper_quantile : float
        Quantile thresholds used for classification. Must satisfy 0 <= lower < upper <= 1.

    Returns
    -------
    List[int]
        Per-trial flags where -1 indicates suppression, 0 no modulation, 1 enhancement.
    """
    observed = row["modulation_index"]
    null_dists = row["modulation_index_null"]
    return [
        modulation_flag(obs, null, lower_quantile=lower_quantile, upper_quantile=upper_quantile)
        for obs, null in zip(observed, null_dists)
    ]

__all__ = [
    "modulation_index_single_trial",
    "compute_trialwise_mi",
    "compute_trialwise_mi_null",
    "modulation_flag",
    "compute_trialwise_modulation_flags",
]

