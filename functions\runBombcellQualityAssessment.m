function runBombcellQualityAssessment(toy_dataset_location, varargin)
% Run Bombcell quality metrics pipeline on a Kilosort4 output folder.
%
% Inputs:
%   toy_dataset_location: full path to Kilosort output folder (string)
%   Optional Name-Value pairs:
%       'SaveClusterKSLabel' (true/false, default = true)
%       'LaunchGUI' (true/false, default = false)
%       'GainToUV' (numeric, default = NaN)
%       'ParamOverrides' (struct, default = struct())

% ---------------- Parse options ----------------
p = inputParser;
addParameter(p, 'SaveClusterKSLabel', true, @islogical);
addParameter(p, 'LaunchGUI', false, @islogical);
addParameter(p, 'GainToUV', NaN, @(x) isnumeric(x) && isscalar(x));
addParameter(p, 'ParamOverrides', struct(), @isstruct);
parse(p, varargin{:});
opt = p.Results;

% ---------------- Setup ----------------
savePath = fullfile(toy_dataset_location, 'bombcell');
kilosortVersion = 4;
ephysKilosortPath = toy_dataset_location;
gain_to_uV = opt.GainToUV;

% ============================================================
% FIX 1: robust meta/raw discovery (use PATHS), wrap meta as dir-like struct
% ============================================================
metaCand = [ ...
    dir(fullfile(toy_dataset_location, '*ap.meta')); ...
    dir(fullfile(toy_dataset_location, '**', '*ap.meta')) ...
    ];
rawCand = [ ...
    dir(fullfile(toy_dataset_location, '*ap.bin')); ...
    dir(fullfile(toy_dataset_location, '**', '*ap.bin')); ...
    dir(fullfile(toy_dataset_location, '*ap.dat')); ...
    dir(fullfile(toy_dataset_location, '**', '*ap.dat')) ...
    ];

assert(~isempty(metaCand), 'No *ap.meta found under: %s', toy_dataset_location);
assert(~isempty(rawCand),  'No *ap.bin or *ap.dat found under: %s', toy_dataset_location);

metaFile = fullfile(metaCand(1).folder, metaCand(1).name);
rawFile  = fullfile(rawCand(1).folder,  rawCand(1).name);

fprintf('[Info] Using meta: %s\n', metaFile);
fprintf('[Info] Using raw : %s\n', rawFile);

% Wrap meta path into a struct (fields .folder / .name) expected by qualityParamValues
[metaFolder, metaBase, metaExt] = fileparts(metaFile);
ephysMetaDir = struct('folder', metaFolder, 'name', [metaBase metaExt]);

% ---------------- Load Kilosort outputs ----------------
[spikeTimes_samples, spikeClusters, templateWaveforms, templateAmplitudes, ...
 pcFeatures, pcFeatureIdx, channelPositions] = ...
    bc.load.loadEphysData(ephysKilosortPath, savePath);

% ---------------- Build Bombcell params ----------------
% NOTE: qualityParamValues expects meta as dir-like struct, raw as path
param = bc.qm.qualityParamValues(ephysMetaDir, rawFile, ...
    ephysKilosortPath, gain_to_uV, kilosortVersion);

% Your defaults (will be refined by meta below or overridden by ParamOverrides)
param.nChannels = 385;
param.nSyncChannels = 1;
param.ephys_sample_rate = 30000;

% Prefer values from SpikeGLX meta if available
try
    meta = bc.dependencies.SGLX_readMeta.ReadMeta(ephysMetaDir.name, ephysMetaDir.folder);
    [AP, ~, SY] = bc.dependencies.SGLX_readMeta.ChannelCountsIM(meta);
    if ~isempty(AP), param.nChannels = AP + SY; end
    if ~isempty(SY), param.nSyncChannels = SY; end

    % Coerce sample rate to a finite scalar double
    if isfield(meta,'imSampRate')
        sr = meta.imSampRate;
        if ischar(sr) || isstring(sr), sr = str2double(sr); end
        if isnumeric(sr) && ~isempty(sr), sr = double(sr(:)); end
        if ~isempty(sr) && isfinite(sr(1)) && sr(1) > 0
            param.ephys_sample_rate = sr(1);
        end
    end
catch ME
    warning('Could not parse SGLX meta. Using defaults. Error: %s', ME.message);
end

% Safety: ensure scalar numeric sample rate
if ~(isnumeric(param.ephys_sample_rate) && isfinite(param.ephys_sample_rate) ...
        && isscalar(param.ephys_sample_rate) && param.ephys_sample_rate > 0)
    warning('ephys_sample_rate invalid; falling back to 30000 Hz.');
    param.ephys_sample_rate = 30000;
end

% ---------------- Quality thresholds (defaults) ----------------
param.minNumSpikes           = 150;
param.minPresenceRatio       = 0.3;
param.lratioMax              = 0.5;
param.maxRPVviolations       = 0.15;
param.maxPercSpikesMissing   = 30;
param.minWvDuration          = 80;
param.maxWvDuration          = 1500;
param.maxWvBaselineFraction  = 0.3;
param.splitGoodAndMua_NonSomatic = 0;

% ---------------- Apply user overrides ----------------
if ~isempty(opt.ParamOverrides)
    fn = fieldnames(opt.ParamOverrides);
    for k = 1:numel(fn)
        param.(fn{k}) = opt.ParamOverrides.(fn{k});
    end
end

% ---------------- Run metrics ----------------
[qMetric, unitType] = bc.qm.runAllQualityMetrics(param, spikeTimes_samples, ...
    spikeClusters, templateWaveforms, templateAmplitudes, ...
    pcFeatures, pcFeatureIdx, channelPositions, savePath);

% ---------------- Save outputs ----------------
if ~exist(savePath, 'dir'), mkdir(savePath); end

% Unit labels (Bombcell type per template index)
unit_label_table = table(unitType);
writetable(unit_label_table, fullfile(savePath, 'templates._bc_unit_labels.tsv'), ...
    'FileType', 'text', 'Delimiter', '\t');

% Optionally save cluster_KSLabel.tsv (map nonSomatic -> 'mua')
if opt.SaveClusterKSLabel
    labelNames = {'noise', 'good', 'mua', 'mua'};
    KSLabel = cell(length(unitType),1);
    for i = 1:length(unitType)
        KSLabel{i} = labelNames{unitType(i)+1};
    end
    cluster_id = (0:length(unitType)-1)';
    cluster_KSLabel_table = table(cluster_id, KSLabel, ...
        'VariableNames', {'cluster_id', 'KSLabel'});
    writetable(cluster_KSLabel_table, fullfile(savePath, '..', 'cluster_KSLabel.tsv'), ...
        'FileType', 'text', 'Delimiter', '\t');
end

% ---------------- Optional GUI (left as-is; requires its own inputs) ----------------
if opt.LaunchGUI
    bc.load.loadMetricsForGUI;
    unitQualityGuiHandle = bc.viz.unitQualityGUI_synced( ... %#ok<NASGU>
        memMapData, ephysData, qMetric, forGUI, rawWaveforms, ...
        param, probeLocation, unitType, loadRawTraces);
end

fprintf('[✓] Finished processing: %s\n', toy_dataset_location);
end
