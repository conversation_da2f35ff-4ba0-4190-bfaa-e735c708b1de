function generateKilosortChanMap(metaFullPath, numShanks, bProcLF)
% generateKilosortChanMap - Creates a Kilosort-compatible channel map file
%
% Syntax:
%   generateKilosortChanMap(metaFullPath, numShanks)
%   generateKilosortChanMap(metaFullPath, numShanks, bProcLF)
%
% Inputs:
%   metaFullPath : Full path to the SpikeGLX .ap.meta file
%   numShanks    : Number of shanks in the probe (typically 1 or 4)
%   bProcLF      : (Optional) Flag for LFP (1) or AP (0, default)
%
% Output:
%   Saves a *_kilosortChanMap.mat file in the same directory as metaFullPath

    if nargin < 3
        bProcLF = 0;
    end

    % Add required path
    addpath(genpath('C:\Users\<USER>\Documents\GitHub\SGLXMetaToCoords'));

    % Output file path
    chanMapFile = replace(metaFullPath, '.ap.meta', '_kilosortChanMap.mat');

    % === Extract coordinates ===
    SGLXMetaToCoords(metaFullPath, bProcLF);

    % === Validate variables ===
    requiredVars = {'chanMap', 'connected', 'xcoords', 'ycoords'};
    for v = requiredVars
        if ~exist(v{1}, 'var')
            error('Variable "%s" not found. Did SGLXMetaToCoords run correctly?', v{1});
        end
    end

    % === Format inputs ===
    xcoords = xcoords(:);
    ycoords = ycoords(:);
    chanMap = chanMap(:);
    connected = connected(:);

    % === Assign kcoords based on number of shanks ===
    kcoords = zeros(size(xcoords));

    switch numShanks
        case 1
            kcoords(:) = 1;

        case 4
            unique_x = unique(xcoords);
            if numel(unique_x) ~= 8
                error('Expected 8 unique x positions (2 per shank), got %d. Check xcoords.', numel(unique_x));
            end

            shank_centers = reshape(unique_x, 2, numShanks)';  % [4 x 2]

            for shank_id = 1:numShanks
                shank_xs = shank_centers(shank_id, :);
                idx = ismember(xcoords, shank_xs);
                kcoords(idx) = shank_id;
            end

        otherwise
            error('Only numShanks == 1 or 4 is supported.');
    end

    % === Save to file ===
    save(chanMapFile, 'chanMap', 'connected', 'xcoords', 'ycoords', 'kcoords');
    fprintf('✅ Saved chanMap with %d shank(s) to:\n%s\n', numShanks, chanMapFile);
end
