{"cells": [{"cell_type": "markdown", "id": "notebook_header", "metadata": {}, "source": ["# Neuropixels: Extract Units and Assign Brain Regions\n", "\n", "This notebook consolidates:\n", "1. Extracting unit data from Kilosort outputs (npx_utils.extract_unit_data)\n", "2. Generating probe trajectory CSVs from AP_histology outputs (npx_utils.extract_probe_trajectory)\n", "3. Assigning brain regions to units based on penetration depth and trajectories\n", "4. Summarizing unit counts per brain region across recordings\n"]}, {"cell_type": "code", "execution_count": 1, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[32;1mnpyx[c4] version 4.1.3 imported.\u001b[0m\n"]}], "source": ["import os\n", "import re\n", "from pathlib import Path\n", "from collections import Counter\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from npx_utils import extract_unit_data, extract_probe_trajectory"]}, {"cell_type": "markdown", "id": "config_header", "metadata": {}, "source": ["## Configuration\n", "Edit the paths and parameters below to match your environment."]}, {"cell_type": "code", "execution_count": null, "id": "configuration", "metadata": {}, "outputs": [], "source": ["# Kilosort dataset folders (each must contain channel_positions.npy, etc.)\n", "dataset_paths = [\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b12\\b12_p1_r1_g0\\b12_p1_r1_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b12\\b12_p2_r1_g0\\b12_p2_r1_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b13\\b13_p1_r2_g0\\b13_p1_r2_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b14\\b14_p1_r1_g0\\b14_p1_r1_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b15\\b15_p1_r1_g0\\b15_p1_r1_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b15\\b15_p2_r1_g0\\b15_p2_r1_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b16\\b16_p1_r1_g0\\b16_p1_r1_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b16\\b16_p2_r1_g0\\b16_p2_r1_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b17\\b17_p1_r1_g0\\b17_p1_r1_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b17\\b17_p1_r2_g0\\b17_p1_r2_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b18\\b18_p1_r1_g0\\b18_p1_r1_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b24\\b24_p1_r1_g0\\b24_p1_r1_g0_imec0\"),\n", "    # Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b25\\b25_p1_r2_g0\\b25_p1_r2_g0_imec0\"),\n", "    Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b27\\b27_p1_r1_g0\\catgt\"),\n", "    Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b28\\b28_p1_r2_g0\\catgt\"),\n", "    Path(r\"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b28\\b28_p2_r1_g0\\catgt\")\n", "]\n", "\n", "# AP_histology outputs to convert into trajectory CSVs (optional – run Step 1)\n", "trajectory_mat_paths = [\n", "    # r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b12\\results\\probe_ccf_struct_1.mat',\n", "    # r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b12\\results\\probe_ccf_struct_2.mat',\n", "    # r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b14\\results\\probe_ccf_struct_1.mat',\n", "    # r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b16\\results\\probe_ccf_struct_1.mat',\n", "    # r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b15\\results\\probe_ccf_struct_1.mat',\n", "    # r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b15\\results\\probe_ccf_struct_2.mat',\n", "    # r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b16\\results\\probe_ccf_struct_2.mat',\n", "    # r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b17\\results\\probe_ccf_struct_1.mat',\n", "    # r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b18\\results\\probe_ccf_struct_1.mat',\n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b24\\results\\probe_ccf_struct_1.mat',\n", "    r'Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b25\\results\\probe_ccf_struct_1.mat'\n", "    \n", "]\n", "\n", "# Recording ID to maximum probe depth (um) – used for penetration depth from tip\n", "recording_to_max_depth = {\n", "    \"b12_p1_r1_g0\": 3400,\n", "    \"b12_p2_r1_g0\": 4700,\n", "    \"b13_p1_r2_g0\": None,\n", "    \"b14_p1_r1_g0\": 3550,\n", "    \"b15_p1_r1_g0\": 2800,\n", "    \"b15_p2_r1_g0\": 4000,\n", "    \"b16_p1_r1_g0\": 3350,\n", "    \"b16_p2_r1_g0\": 3750,\n", "    \"b17_p1_r1_g0\": 3950,\n", "    \"b17_p1_r2_g0\": 3950,\n", "    \"b18_p1_r1_g0\": 3330,\n", "    \"b24_p1_r1_g0\": 1200,\n", "    \"b25_p1_r2_g0\": 1200,\n", "}\n", "\n", "# Recording ID to animal ID mapping\n", "recording_to_animal_id = {\n", "    \"b12_p1_r1_g0\": \"b12\",\n", "    \"b12_p2_r1_g0\": \"b12\",\n", "    \"b14_p1_r1_g0\": \"b14\",\n", "    \"b15_p1_r1_g0\": \"b15\",\n", "    \"b15_p2_r1_g0\": \"b15\",\n", "    \"b16_p1_r1_g0\": \"b16\",\n", "    \"b16_p2_r1_g0\": \"b16\",\n", "    \"b17_p1_r1_g0\": \"b17\",\n", "    \"b17_p1_r2_g0\": \"b17\",\n", "    \"b18_p1_r1_g0\": \"b18\",\n", "    \"b24_p1_r1_g0\": \"b24\",\n", "    \"b25_p1_r2_g0\": \"b25\",\n", "}\n", "\n", "# Recording ID to trajectory CSV (created in Step 1)\n", "recording_to_trajectory = {\n", "    \"b12_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b12\\results\\trajectory_1.csv\",\n", "    \"b12_p2_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b12\\results\\trajectory_2.csv\",\n", "    \"b14_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b14\\results\\trajectory_1.csv\",\n", "    \"b15_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b15\\results\\trajectory_1.csv\",\n", "    \"b15_p2_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b15\\results\\trajectory_2.csv\",\n", "    \"b16_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b16\\results\\trajectory_1.csv\",\n", "    \"b16_p2_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b16\\results\\trajectory_2.csv\",\n", "    \"b17_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b17\\results\\trajectory_1.csv\",\n", "    \"b17_p1_r2_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b17\\results\\trajectory_1.csv\",\n", "    \"b18_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b18\\results\\trajectory_1.csv\",\n", "    \"b24_p1_r1_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b24\\results\\trajectory_1.csv\",\n", "    \"b24_p1_r1_g0_post2000\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b24\\results\\trajectory_1.csv\",\n", "    \"b25_p1_r2_g0\": r\"Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b25\\results\\trajectory_1.csv\",\n", "}\n", "\n", "# Parameters\n", "MIN_FIRING_RATE = 0.05  # Hz\n", "DISTANCE_TIP_TO_FIRST_CONTACT = 0  # um"]}, {"cell_type": "markdown", "id": "step1_header", "metadata": {}, "source": ["## Step 1: Generate trajectory CSVs from AP_histology outputs (optional)\n", "This converts `probe_ccf_struct_#.mat` to easy-to-use `trajectory_#.csv` files."]}, {"cell_type": "code", "execution_count": 10, "id": "step1_generation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✔ Saved trajectory: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b24\\results\\trajectory_1.csv\n", "✔ Saved trajectory: Z:\\users\\izouridis\\projects\\bal_npx\\tracks\\b25\\results\\trajectory_1.csv\n"]}], "source": ["def trajectory_csv_path(mat_path: str) -> Path:\n", "    s = Path(mat_path).stem  # e.g., 'probe_ccf_struct_1'\n", "    m = re.search(r'_(\\d+)$', s)\n", "    idx = m.group(1) if m else '1'\n", "    return Path(mat_path).parent / f'trajectory_{idx}.csv'\n", "\n", "for mp in trajectory_mat_paths:\n", "    try:\n", "        df_traj = extract_probe_trajectory(mp)  # also saves CSV next to .mat by default\n", "        csv_out = trajectory_csv_path(mp)\n", "        # Ensure CSV is present at expected name\n", "        df_traj.to_csv(csv_out, index=False)\n", "        print(f'✔ Saved trajectory: {csv_out}')\n", "    except Exception as e:\n", "        print(f'⚠ Failed trajectory for {mp}: {e}')"]}, {"cell_type": "markdown", "id": "step2_header", "metadata": {}, "source": ["## Step 2: Extract units and save unit_summary.pkl per dataset\n", "Filters units by mean firing rate (MIN_FIRING_RATE)."]}, {"cell_type": "code", "execution_count": 11, "id": "step2_units", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing units: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b24\\b24_p1_r1_g0\\b24_p1_r1_g0_imec0\n", "[ 35/35] 100.00% | Unit 41 | ETC: 00:004\n", "✅ Done.\n", "  ✔ Saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b24\\b24_p1_r1_g0\\b24_p1_r1_g0_imec0\\unit_summary.pkl\n", "Processing units: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b25\\b25_p1_r2_g0\\b25_p1_r2_g0_imec0\n", "\n", "\u001b[34;1m--- New spike-sorting detected.\u001b[0m\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[  1/14]   7.14% | Unit 36 | ETC: 02:33\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[  2/14]  14.29% | Unit 39 | ETC: 02:01\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[  3/14]  21.43% | Unit 40 | ETC: 01:49\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[  4/14]  28.57% | Unit 51 | ETC: 01:38\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[  5/14]  35.71% | Unit 63 | ETC: 01:16\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[  6/14]  42.86% | Unit 107 | ETC: 01:10\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[  7/14]  50.00% | Unit 113 | ETC: 01:03\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[  8/14]  57.14% | Unit 114 | ETC: 00:54\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[  9/14]  64.29% | Unit 127 | ETC: 00:46\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[ 10/14]  71.43% | Unit 153 | ETC: 00:37\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[ 11/14]  78.57% | Unit 156 | ETC: 00:28\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[ 12/14]  85.71% | Unit 160 | ETC: 00:18\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[ 13/14]  92.86% | Unit 163 | ETC: 00:09\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "\u001b[31mHeads-up, the voltage range seems to be 1240000.0, which is not the default (10^6). Might be normal!\n", "[ 14/14] 100.00% | Unit 167 | ETC: 00:00\n", "✅ Done.\n", "  ✔ Saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b25\\b25_p1_r2_g0\\b25_p1_r2_g0_imec0\\unit_summary.pkl\n"]}], "source": ["for datapath in dataset_paths:\n", "    print(f'Processing units: {datapath}')\n", "    try:\n", "        recording_id_from_path = lambda p: p.name.split('_imec')[0]\n", "        rid = recording_id_from_path(datapath)\n", "        animal = recording_to_animal_id.get(rid)\n", "        df = extract_unit_data(datapath=str(datapath), animal_id=animal, quality=['good', 'mua'])\n", "        df = df[df['mean_firing_rate'] > MIN_FIRING_RATE]\n", "        out_pkl = datapath / 'unit_summary.pkl'\n", "        df.to_pickle(out_pkl)\n", "        print(f'  ✔ Saved: {out_pkl}')\n", "    except Exception as e:\n", "        print(f'  ❌ Failed: {e}')"]}, {"cell_type": "markdown", "id": "step3_header", "metadata": {}, "source": ["## Step 3: Assign brain regions to units and save updated DataFrames\n", "Computes penetration depth from tip, assigns regions via trajectory intervals, and saves the updated unit_summary.pkl per recording."]}, {"cell_type": "code", "execution_count": 12, "id": "step3_regions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Assigning regions: b24_p1_r1_g0\n", "  🧠 Units per region:\n", "    Primary somatosensory area lower limb layer 6a: 31\n", "    Primary somatosensory area lower limb layer 5: 4\n", "  ✔ Updated and saved\n", "Assigning regions: b25_p1_r2_g0\n", "  🧠 Units per region:\n", "    Primary somatosensory area lower limb layer 6a: 12\n", "    Primary somatosensory area lower limb layer 5: 2\n", "  ✔ Updated and saved\n"]}], "source": ["def recording_id_from_path(p: Path) -> str:\n", "    # folder name before '_imec' suffix\n", "    return p.name.split('_imec')[0]\n", "\n", "\n", "for datapath in dataset_paths:\n", "    out_pkl = datapath / 'unit_summary.pkl'\n", "    rid = recording_id_from_path(datapath)\n", "    print(f'Assigning regions: {rid}')\n", "\n", "    max_depth = recording_to_max_depth.get(rid)\n", "    if not max_depth:\n", "        print('  ⚠ Skipped: no max depth')\n", "        continue\n", "\n", "    track_csv = recording_to_trajectory.get(rid)\n", "    if not track_csv or not os.path.exists(track_csv):\n", "        print('  ⚠ Skipped: missing trajectory CSV')\n", "        continue\n", "\n", "    try:\n", "        unit_df = pd.read_pickle(out_pkl)\n", "        track_df = pd.read_csv(track_csv)\n", "\n", "        # Compute penetration depth from tip\n", "        unit_df['depth_penetration_um'] = (\n", "            max_depth - DISTANCE_TIP_TO_FIRST_CONTACT - unit_df['depth']\n", "        )\n", "\n", "        # Vectorized interval lookup using IntervalIndex\n", "        iv = pd.IntervalIndex.from_arrays(track_df['depth_from'], track_df['depth_to'], closed='left')\n", "        idx = iv.get_indexer(unit_df['depth_penetration_um'])\n", "        # Map indices to region columns; set NA where idx == -1\n", "        region_name = pd.Series(pd.NA, index=unit_df.index)\n", "        region_acr = pd.Series(pd.NA, index=unit_df.index)\n", "        valid = idx >= 0\n", "        region_name[valid] = track_df['region_name'].values[idx[valid]]\n", "        region_acr[valid] = track_df['region_acronym'].values[idx[valid]]\n", "        unit_df['region_name'] = region_name\n", "        unit_df['region_acronym'] = region_acr\n", "\n", "        # Save back\n", "        unit_df.to_pickle(out_pkl)\n", "\n", "        # Per-recording summary\n", "        counts = unit_df['region_name'].dropna().value_counts()\n", "        if not counts.empty:\n", "            print('  🧠 Units per region:')\n", "            for r, c in counts.items():\n", "                print(f'    {r}: {c}')\n", "        print('  ✔ Updated and saved')\n", "    except Exception as e:\n", "        print(f'  ❌ Failed: {e}')"]}, {"cell_type": "markdown", "id": "step4_header", "metadata": {}, "source": ["## Step 4: Generate summary statistics across recordings\n", "Loads processed unit_summary.pkl files and computes overall region counts."]}, {"cell_type": "code", "execution_count": 13, "id": "step4_summary", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Total units per region (all recordings):\n", "  Primary somatosensory area lower limb layer 6a: 43\n", "  Primary somatosensory area lower limb layer 5: 6\n"]}], "source": ["from collections import Counter\n", "overall_counts = Counter()\n", "for datapath in dataset_paths:\n", "    out_pkl = datapath / 'unit_summary.pkl'\n", "    if not out_pkl.exists():\n", "        print(f\"⚠ Skipped: missing {out_pkl}\")\n", "        continue\n", "    try:\n", "        unit_df = pd.read_pickle(out_pkl)\n", "        counts = unit_df['region_name'].dropna().value_counts()\n", "        for r, c in counts.items():\n", "            overall_counts[r] += c\n", "    except Exception as e:\n", "        print(f\"  ❌ Failed: {e}\")\n", "\n", "print(\"\\n📊 Total units per region (all recordings):\")\n", "for r, c in overall_counts.most_common():\n", "    print(f\"  {r}: {c}\")\n"]}, {"cell_type": "markdown", "id": "step5_header", "metadata": {}, "source": ["## Step 5: Attach balloon on/off annotations to unit_summary.pkl (per recording)\n", "Loads bal_on_sec.txt and bal_off_sec.txt from the recording base folder and stores them in each unit's row."]}, {"cell_type": "code", "execution_count": 14, "id": "step5_balloon", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✔ Added balloon annotations to b24_p1_r1_g0 (4 on, 4 off intervals)\n", "✔ Added balloon annotations to b25_p1_r2_g0 (11 on, 11 off intervals)\n"]}], "source": ["from pathlib import Path\n", "import numpy as np\n", "import pandas as pd\n", "\n", "def recording_id_from_path(p: Path) -> str:\n", "    return p.name.split('_imec')[0]\n", "\n", "# Process all recordings in dataset_paths\n", "\n", "for datapath in dataset_paths:\n", "    rid = recording_id_from_path(datapath)\n", "\n", "    basepath = datapath.parent  # folder containing the imec0 subfolder\n", "    on_path = basepath / 'bal_on_sec.txt'\n", "    off_path = basepath / 'bal_off_sec.txt'\n", "    out_pkl = datapath / 'unit_summary.pkl'\n", "\n", "    if not on_path.exists() or not off_path.exists():\n", "        print(f\"⚠ Missing balloon files for {rid}: {on_path.name} / {off_path.name}\")\n", "        continue\n", "    if not out_pkl.exists():\n", "        print(f\"⚠ Missing {out_pkl} — run Step 2 first.\")\n", "        continue\n", "\n", "    try:\n", "        bal_on = np.loadtxt(on_path, delimiter='\t')\n", "        bal_off = np.loadtxt(off_path, delimiter='\t')\n", "        bal_on = np.atleast_2d(bal_on)\n", "        bal_off = np.atleast_2d(bal_off)\n", "\n", "        # Load, attach columns, save, and validate persistence\n", "        df = pd.read_pickle(out_pkl)\n", "        df['balloon_on_sec'] = [bal_on.tolist()] * len(df)\n", "        df['balloon_off_sec'] = [bal_off.tolist()] * len(df)\n", "        assert 'balloon_on_sec' in df.columns and 'balloon_off_sec' in df.columns\n", "        df.to_pickle(out_pkl)\n", "        # re-load and validate columns persisted\n", "        df_check = pd.read_pickle(out_pkl)\n", "        persisted = set(['balloon_on_sec','balloon_off_sec']).issubset(df_check.columns)\n", "        if not persisted:\n", "            raise RuntimeError('Balloon columns did not persist to pickle.')\n", "        print(f\"✔ Added balloon annotations to {rid} ({len(bal_on)} on, {len(bal_off)} off intervals)\")\n", "    except Exception as e:\n", "        print(f\"❌ Failed {rid}: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}