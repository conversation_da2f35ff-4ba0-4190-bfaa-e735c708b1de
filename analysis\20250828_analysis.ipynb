# Standard library imports
from pathlib import Path
from typing import List, Dict, Optional
import os
from datetime import datetime

# Third-party imports
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.colors import TwoSlopeNorm
from tabulate import tabulate

import sys
functions_path = Path.cwd().parent / "functions"
sys.path.append(str(functions_path))

# Local module imports
from npx_utils import extract_unit_data
from modulation_functions import (
    modulation_index_single_trial,
    compute_trialwise_mi,
    compute_trialwise_mi_null,
    modulation_flag,
    compute_trialwise_modulation_flags
)
from plotting_functions import (
    plot_units_grid_with_shading, 
    _generate_plot_filename,
    plot_mi_heatmap,
    plot_unit_classification_pie_chart_all,
    plot_unit_classification_pie_chart_per_recording,
    plot_recording_waveforms
)

# ========================================
# PROCESSING MODE CONFIGURATION
# ========================================
# Set to True for full processing (execute balloon event integration)
# Set to False to skip balloon integration and use pre-processed data
PROCESS_UNIT_EXTRACTION = False
PROCESS_BALLOON_EVENTS = True

# ========================================
# ANALYSIS CONFIGURATION
# ========================================
MINIMUM_FIRING_RATE_HZ = 0.05
BALLOON_OFFSET_DURATION_SEC = 200.0
NULL_DISTRIBUTION_ITERATIONS = 10000
MODULATION_WINDOW_LENGTH_SAMPLES = 40
RANDOM_SEED = 42

# Time limits (seconds) for filtering balloon events per animal
# -1 indicates no time limit
TIME_LIMITS_BY_ANIMAL = {
    "b24": 2000, 
    "b25": 2000, 
    "b27": 4000,
    "b28": 4000
}

DATASET_PATHS = [
    Path(r'Z:\users\izouridis\projects\bal_npx\data\b24\b24_p1_r1_g0\b24_p1_r1_g0_imec0'),
    Path(r'Z:\users\izouridis\projects\bal_npx\data\b25\b25_p1_r2_g0\b25_p1_r2_g0_imec0'),
    Path(r'Z:\users\izouridis\projects\bal_npx\data\b27\b27_p1_r1_g0\catgt'),
    Path(r'Z:\users\izouridis\projects\bal_npx\data\b28\b28_p1_r2_g0\catgt')
]

# Mapping from recording directory names to animal IDs
RECORDING_TO_ANIMAL_MAPPING = {
    "b24_p1_r1_g0": "b24",
    "b25_p1_r2_g0": "b25",
    "b27_p1_r1_g0": "b27",
    "b28_p1_r2_g0": "b28"
}

if PROCESS_UNIT_EXTRACTION:
    print("🔄 FULL PROCESSING MODE: Extracting units from Kilosort output...")

    # Extract unit data from Kilosort datasets and filter by firing rate
    for dataset_path in DATASET_PATHS:
        recording_id = dataset_path.parent.name
        animal_id = RECORDING_TO_ANIMAL_MAPPING.get(recording_id, "")
        
        print(f"[Unit Extraction] Processing: {dataset_path}")
        print(f"  Recording ID: {recording_id}, Animal ID: {animal_id}")
        
        try:
            # Extract unit data with good and MUA quality using imported function
            unit_dataframe = extract_unit_data(
                datapath=str(dataset_path), 
                animal_id=animal_id, 
                quality=['good', 'mua']
            )
            
            # Filter by minimum firing rate
            units_before_filter = len(unit_dataframe)
            unit_dataframe = unit_dataframe[
                unit_dataframe['mean_firing_rate'] > MINIMUM_FIRING_RATE_HZ
            ]
            units_after_filter = len(unit_dataframe)
            
            # Save filtered data
            output_file = dataset_path / 'unit_summary.pkl'
            unit_dataframe.to_pickle(output_file)
            
            print(f"  ✔ Filtered {units_before_filter} → {units_after_filter} units")
            print(f"  ✔ Saved: {output_file}")
            
        except Exception as error:
            print(f"  ❌ Failed to process {dataset_path}: {error}")
            
else:
    print("⏭️  SKIP PROCESSING MODE: Unit extraction skipped.")
    print("   Assuming unit summary files already exist.")
    print("   If this is incorrect, set PROCESS_UNIT_EXTRACTION = True and re-run this cell.")
    print()

# Check processing mode and display current configuration
if PROCESS_BALLOON_EVENTS:
    print("🔄 FULL PROCESSING MODE: Executing balloon event integration...")
    print(f"   Processing {len(DATASET_PATHS)} dataset(s)")
    print()
    
    # Load balloon event timing data and integrate with unit summaries
    for dataset_path in DATASET_PATHS:
        recording_id = dataset_path.parent.name
        animal_id = RECORDING_TO_ANIMAL_MAPPING.get(recording_id, "")
        base_path = dataset_path.parent
        
        # Define file paths
        balloon_on_file = base_path / 'bal_on_sec.txt'
        balloon_off_file = base_path / 'bal_off_sec.txt'
        unit_summary_file = dataset_path / 'unit_summary.pkl'
        
        status_prefix = f"[Balloon Integration] {recording_id} ({animal_id})"
        
        # Validation checks
        if not animal_id:
            print(f"{status_prefix} ❌ Animal ID not found. Skipping.")
            continue
            
        if not balloon_on_file.exists() or not balloon_off_file.exists():
            print(f"{status_prefix} ⚠ Missing balloon timing files")
            continue
            
        if not unit_summary_file.exists():
            print(f"{status_prefix} ⚠ Missing unit summary file - run extraction first")
            continue
        
        try:
            # Load balloon timing data
            balloon_on_times = np.atleast_2d(np.loadtxt(balloon_on_file, delimiter='\t'))
            balloon_off_times = np.atleast_2d(np.loadtxt(balloon_off_file, delimiter='\t'))
            events_before_filter = len(balloon_on_times)
            
            # Apply time limits if specified
            time_limit = float(TIME_LIMITS_BY_ANIMAL.get(animal_id, -1))
            if time_limit > 0:
                time_mask = (
                    (balloon_on_times[:, 0] < time_limit) & 
                    (balloon_on_times[:, 1] < time_limit) &
                    (balloon_off_times[:, 0] < time_limit) & 
                    (balloon_off_times[:, 1] < time_limit)
                )
                balloon_on_times = balloon_on_times[time_mask]
                balloon_off_times = balloon_off_times[time_mask]
            
            events_after_filter = len(balloon_on_times)
            
            # Load unit data and add balloon timing columns
            unit_dataframe = pd.read_pickle(unit_summary_file)
            unit_dataframe['balloon_on_sec'] = [balloon_on_times.tolist()] * len(unit_dataframe)
            unit_dataframe['balloon_off_sec'] = [balloon_off_times.tolist()] * len(unit_dataframe)
            
            # Save updated data
            unit_dataframe.to_pickle(unit_summary_file)
            
            print(f"{status_prefix} ✔ Integrated {events_before_filter}→{events_after_filter} events (time_limit={time_limit})")
            
        except Exception as error:
            print(f"{status_prefix} ❌ Integration failed: {error}")
            
    print("\n✅ Balloon event integration completed.")
    
else:
    print("⏭️  SKIP PROCESSING MODE: Balloon event integration skipped.")
    print("   Assuming unit summary files already contain balloon event data.")
    print("   If this is incorrect, set PROCESS_BALLOON_EVENTS = True and re-run this cell.")
    print()

# Generate summary table of unit counts and balloon events for each dataset
summary_data = []

for dataset_path in DATASET_PATHS:
    recording_id = dataset_path.parent.name
    animal_id = RECORDING_TO_ANIMAL_MAPPING.get(recording_id, "")
    unit_summary_file = dataset_path / "unit_summary.pkl"
    
    if not unit_summary_file.exists():
        continue
    
    try:
        unit_dataframe = pd.read_pickle(unit_summary_file)
        
        # Count units by quality
        good_unit_count = (
            int((unit_dataframe.get("quality") == "good").sum()) 
            if "quality" in unit_dataframe else None
        )
        mua_unit_count = (
            int((unit_dataframe.get("quality") == "mua").sum()) 
            if "quality" in unit_dataframe else None
        )
        
        # Count balloon events
        balloon_on_count = (
            len(unit_dataframe.get("balloon_on_sec", [[]])[0]) 
            if "balloon_on_sec" in unit_dataframe else 0
        )
        balloon_off_count = (
            len(unit_dataframe.get("balloon_off_sec", [[]])[0]) 
            if "balloon_off_sec" in unit_dataframe else 0
        )
        
        summary_data.append({
            "recording_id": recording_id,
            "animal_id": animal_id,
            "units": len(unit_dataframe),
            "good_units": good_unit_count,
            "mua_units": mua_unit_count,
            "balloon_on": balloon_on_count,
            "balloon_off": balloon_off_count,
        })
        
    except Exception as error:
        print(f"  ⚠ Summary generation failed for {recording_id}: {error}")

# Create and display summary DataFrame
dataset_summary = pd.DataFrame(summary_data)

if not dataset_summary.empty:
    print("\n=== Dataset Summary ===\n")
    print(tabulate(dataset_summary, headers="keys", tablefmt="github", showindex=True))
else:
    print("No summary data available - check dataset paths and processing steps.")

# Define paths to unit summary files
unit_summary_paths = {
    # Uncomment additional datasets as needed
    'b24': Path(r'Z:\users\izouridis\projects\bal_npx\data\b24\b24_p1_r1_g0\b24_p1_r1_g0_imec0\unit_summary.pkl'),
    'b25': Path(r'Z:\users\izouridis\projects\bal_npx\data\b25\b25_p1_r2_g0\b25_p1_r2_g0_imec0\unit_summary.pkl'),
    'b27': Path(r'Z:\users\izouridis\projects\bal_npx\data\b27\b27_p1_r1_g0\catgt\unit_summary.pkl'),
    'b28': Path(r'Z:\users\izouridis\projects\bal_npx\data\b28\b28_p1_r2_g0\catgt\unit_summary.pkl')
}

# Load data from existing files
dataframes = []
available_datasets = []

for dataset_name, file_path in unit_summary_paths.items():
    if not file_path.exists():
        print(f"⚠ Missing file: {file_path}")
        continue
        
    unit_dataframe = pd.read_pickle(file_path).copy()
    dataframes.append(unit_dataframe)
    available_datasets.append(dataset_name)

# Combine all datasets
if dataframes:
    neural_data = pd.concat(dataframes, ignore_index=True)
else:
    neural_data = pd.DataFrame()

print(f"Loaded {len(neural_data)} units from datasets: {available_datasets}")

if not neural_data.empty:
    # Create unique unit identifiers
    neural_data['unit'] = (
        neural_data['animal_id'] + '_' + neural_data['unit'].astype(str)
    )
    
    # Remove unnecessary columns to reduce memory usage
    columns_to_remove = ['spike_times_samples', 'peak_channel', 'depth']
    existing_columns_to_remove = [
        col for col in columns_to_remove if col in neural_data.columns
    ]
    neural_data.drop(columns=existing_columns_to_remove, inplace=True)
    
    # Adjust balloon OFF timing by subtracting offset duration
    neural_data["balloon_off_sec"] = neural_data["balloon_off_sec"].apply(
        lambda intervals: [
            [max(end_time - BALLOON_OFFSET_DURATION_SEC, 0.0), max(end_time, 0.0)] 
            if np.isfinite(end_time) else [start_time, end_time] 
            for start_time, end_time in intervals
        ]
    )

# Perform modulation analysis if data is available
if not neural_data.empty:
    
    # Calculate observed modulation indices
    neural_data['modulation_index'] = neural_data.apply(compute_trialwise_mi, axis=1)
    
    print("Generating null distributions for statistical testing...")
    
    # Generate null distributions for statistical significance testing
    neural_data['modulation_index_null'] = neural_data.apply(
        lambda row: compute_trialwise_mi_null(
            row,
            window_len=MODULATION_WINDOW_LENGTH_SAMPLES,
            n_iter=NULL_DISTRIBUTION_ITERATIONS,
            mi_formula='difference_over_sum',
            seed=RANDOM_SEED
        ),
        axis=1
    )
    
    print("Determining modulation significance flags...")
    
    # Determine statistical significance flags for each trial using imported function
    # -1: significantly suppressed, 0: not modulated, 1: significantly enhanced
    neural_data['modulated_trial'] = neural_data.apply(
        lambda row: compute_trialwise_modulation_flags(
            row, 
            lower_quantile=0.1, 
            upper_quantile=0.9
        ),
        axis=1
    )
    
    print("Modulation analysis complete.")
else:
    print("No data available for modulation analysis.")

# Classify units to excited/inhibited/mixed

from modulation_functions import classify_unit

neural_data['unit_class'] = neural_data['modulated_trial'].apply(classify_unit)

# Plot modulation index heatmap
plot_mi_heatmap(neural_data, date='20250828')

# Plot unit classification pie chart for all units
plot_unit_classification_pie_chart_all(neural_data, date='20250828', show_plot=True)


# Plot unit classification pie charts per recording
plot_unit_classification_pie_chart_per_recording(neural_data, date='20250828', show_plot=True)

from plotting_functions import plot_units_grid_with_shading

# Build a mapping from unit id -> unit class for border coloring
unit_class_map = dict(zip(neural_data['unit'], neural_data['unit_class']))

# Create one composite figure per recording (animal_id)
grid_params = dict(
    bin_size=10, t_start=0, t_end=None, n_cols=4,
    figsize_per_subplot=(4.5, 2.0), wspace=0.2, hspace=0.4,
    margins=(0.06, 0.92, 0.08, 0.9), dpi=200
)
for animal, df_sub in neural_data.groupby('animal_id'):
    out_file = f'{animal}_modulation_units_grid'
    suptitle = f'{animal}: FR with balloon intervals'
    _ = plot_units_grid_with_shading(
        df_sub,
        bin_size=grid_params['bin_size'], t_start=grid_params['t_start'], t_end=grid_params['t_end'],
        n_cols=grid_params['n_cols'], figsize_per_subplot=grid_params['figsize_per_subplot'],
        wspace=grid_params['wspace'], hspace=grid_params['hspace'], margins=grid_params['margins'],
        suptitle=suptitle, out_path=out_file, dpi=grid_params['dpi'],
        unit_class_map=unit_class_map, unit_col_name='unit', 
        use_standard_filenames=True, date='20250828', show_plot=True
    )

# Build dictionary of the recordings of interest
recordings = {
    "b24": neural_data[neural_data["animal_id"] == "b24"],
    "b25": neural_data[neural_data["animal_id"] == "b25"],
    "b27": neural_data[neural_data["animal_id"] == "b27"],
    "b28": neural_data[neural_data["animal_id"] == "b28"],
}

# Plot waveforms for each recording using the imported function
plot_recording_waveforms("b24", recordings["b24"], save=True, date='20250828', show_plot=True)
plot_recording_waveforms("b25", recordings["b25"], save=True, date='20250828', show_plot=True)
plot_recording_waveforms("b27", recordings["b27"], save=True, date='20250828', show_plot=True)
plot_recording_waveforms("b28", recordings["b28"], save=True, date='20250828', show_plot=True)
