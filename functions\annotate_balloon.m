%% This is a script for annotating baloon on and off periods
% Before running this script, I should have used get_analog_channels.ipynb 
% to extract signals (including the piezo signal) into .mat files

addpath('C:\Users\<USER>\Documents\GitHub\fieldtrip')

% List of basepaths to process
basepaths = {
    'Z:\users\izouridis\projects\bal_npx\data\b15\b15_p1_r1_g0\';
    'Z:\users\izouridis\projects\bal_npx\data\b15\b15_p2_r1_g0\';
    'Z:\users\izouridis\projects\bal_npx\data\b12\b12_p1_r1_g0\';
    'Z:\users\izouridis\projects\bal_npx\data\b12\b12_p2_r1_g0\';
    'Z:\users\izouridis\projects\bal_npx\data\b14\b14_p1_r1_g0\';
    'Z:\users\izouridis\projects\bal_npx\data\b17\b17_p1_r1_g0\';
    'z:\users\izouridis\projects\bal_npx\data\b16\b16_p1_r1_g0\';
    'Z:\users\izouridis\projects\bal_npx\data\b16\b16_p2_r1_g0\';
    'Z:\users\izouridis\projects\bal_npx\data\b17\b17_p1_r2_g0\';
    'Z:\users\izouridis\projects\bal_npx\data\b18\b18_p1_r1_g0\';
    'Z:\users\izouridis\projects\bal_npx\data\b24\b24_p1_r1_g0';
    'Z:\users\izouridis\projects\bal_npx\data\b24\b24_p1_r1_g0_p';
    'Z:\users\izouridis\projects\bal_npx\data\b25\b25_p1_r2_g0';
    'Z:\users\izouridis\projects\bal_npx\data\b27\b27_p1_r1_g0';
    'Z:\users\izouridis\projects\bal_npx\data\b28\b28_p1_r2_g0';
    'Z:\users\izouridis\projects\bal_npx\data\b28\b28_p2_r1_g0';
    'Z:\users\izouridis\projects\bal_npx\data\b24\b24_p1_r1_g0_p';
    'Z:\users\izouridis\projects\bal_npx\data\b30\b30_p1_r1_g0';
};


resamplefs = 1000;

for i = numel(basepaths)
    
    basepath = basepaths{i};
    if ~isfolder(basepath), warning('Missing: %s', basepath); continue; end
    fprintf('\nProcessing: %s\n', basepath);
    
    try
        % --- Load channel 1 ---
        load(fullfile(basepath, 'channel_1.mat'));

        % --- Create FieldTrip data structure ---
        data = [];
        data.label{1} = 'test';
        data.trial{1} = channel_1;
        data.time{1} = time_axis;
        data.fsample = 30303;

        % --- Preprocess (demean) ---
        cfg = [];
        cfg.demean = 'yes';
        data_demeaned = ft_preprocessing(cfg, data);

        % --- Resample ---
        cfg = [];
        cfg.resamplefs = resamplefs;
        data_resampled = ft_resampledata(cfg, data_demeaned);

        % --- Manual annotation ---
        cfg = [];
        cfg.artfctdef.bal_on.artifact = [];
        cfg.artfctdef.bal_off.artifact = [];
        cfg.viewmode = 'vertical';
        cfg.blocksize = 100;  % in seconds
        cfg = ft_databrowser(cfg, data_resampled);

        % --- Extract annotations in seconds ---
        bal_on_sec = cfg.artfctdef.bal_on.artifact / resamplefs;
        bal_off_sec = cfg.artfctdef.bal_off.artifact / resamplefs;

        % --- Save annotation files ---
        writematrix(bal_on_sec, fullfile(basepath, 'bal_on_sec.txt'), 'Delimiter', '\t');
        writematrix(bal_off_sec, fullfile(basepath, 'bal_off_sec.txt'), 'Delimiter', '\t');

        fprintf('Saved annotations for: %s\n', basepath);
    catch ME
        fprintf('⚠️ Error processing %s: %s\n', basepath, ME.message);
    end
end
