import os
import time
import warnings
from typing import Union, List, Optional
import numpy as np
import pandas as pd
from npyx.gl import get_units
from npyx.spk_t import trn, mean_firing_rate
from npyx.spk_wvf import wvf, get_peak_chan


def extract_unit_data(datapath: str,
                      animal_id: Optional[str] = None,
                      savepath: Optional[str] = None,
                      fs: int = 30000,
                      periods='all',
                      quality: Union[str, List[str]] = 'good'):
    """
    Extract spike trains, waveforms, firing rates, depths, and quality label for each unit.

    Parameters
    ----------
    datapath : str
        Path to the Kilosort output / recording directory.
    animal_id : str or None, optional
        Identifier of the animal; if provided, included in the output DataFrame as 'animal_id'.
    savepath : str or None, optional
        If provided, save the resulting DataFrame to this pickle path.
    fs : int, optional
        Sampling rate in Hz (default 30000).
    periods : str or list, optional
        Time intervals in seconds to include, or 'all' to include entire recording.
    quality : str or list of str, optional
        Unit quality tags to include, e.g. 'good' or ['good', 'mua'].
        - Single string remains supported for backward compatibility.
        - Passing 'all' is deprecated; use e.g. ['good', 'mua', 'noise'] instead.

    Returns
    -------
    pd.DataFrame
        One row per unit with columns:
        - 'unit' (str)
        - 'animal_id' (str or None): the provided animal identifier (if any)
        - 'quality' (str): unit quality label from npyx
        - 'mean_firing_rate' (float, Hz)
        - 'peak_channel' (int)
        - 'depth' (int, micrometers)
        - 'spike_times_samples' (np.ndarray)
        - 'spike_times_sec' (np.ndarray)
        - 'waveform_mean' (np.ndarray)
        - 'waveform_time_vector_ms' (np.ndarray)

    Examples
    --------
    df = extract_unit_data(r".../imec0", quality='good')
    df = extract_unit_data(r".../imec0", quality=['good', 'mua'])
    df = extract_unit_data(r".../imec0", animal_id='b12', quality=['good','mua'])
    """
    # --- Load channel positions ---
    channel_pos_file = os.path.join(datapath, "channel_positions.npy")
    if not os.path.isfile(channel_pos_file):
        raise FileNotFoundError(f"Channel positions file not found: {channel_pos_file}")
    channel_positions = np.load(channel_pos_file)  # shape (n_channels, 2)

    # --- Normalize and gather units by quality ---
    if isinstance(quality, str):
        if quality.lower() == 'all':
            warnings.warn(
                "quality='all' is deprecated; pass a list like ['good','mua','noise']. "
                "Proceeding with ['good','mua','noise'].",
                DeprecationWarning,
                stacklevel=2,
            )
            qualities = ['good', 'mua', 'noise']
        else:
            qualities = [quality]
    else:
        qualities = list(quality)

    quality_map = {}
    units = []
    for q in qualities:
        try:
            q_units = get_units(datapath, quality=q)
        except Exception as e:
            raise RuntimeError(f"Error fetching units for quality '{q}': {e}")
        for u in q_units:
            if u not in quality_map:
                quality_map[u] = q
                units.append(u)

    if len(units) == 0:
        raise ValueError(f"No units found with quality={qualities} in: {datapath}")

    n_units = len(units)
    data_units = []
    start_time = time.time()

    for i, u in enumerate(units, 1):
        try:
            # Spike times
            t_raw = trn(datapath, u, periods=periods)
            t_sec = t_raw / fs

            # Mean firing rate
            mfr = mean_firing_rate(t_raw, fs=fs)

            # Peak channel and depth
            peakchan = int(get_peak_chan(datapath, u))
            depth = int(channel_positions[peakchan, 1])

            # Waveforms
            waves = wvf(datapath, u, n_waveforms=1000, t_waveforms=90)
            waves_on_peak = waves[:, :, peakchan]
            mean_wvf = np.mean(waves_on_peak, axis=0).astype(np.float64)

            # Time vector for waveform (ms)
            t_wvf = np.linspace(0, 1000 * len(mean_wvf) / fs, len(mean_wvf)).astype(np.float64)

            data_units.append({
                'unit': str(u),
                'animal_id': animal_id,
                'quality': str(quality_map.get(u, 'unknown')),
                'mean_firing_rate': mfr,
                'peak_channel': peakchan,
                'depth': depth,
                'spike_times_samples': t_raw,
                'spike_times_sec': t_sec,
                'waveform_mean': mean_wvf,
                'waveform_time_vector_ms': t_wvf,
            })

            # Progress
            elapsed = time.time() - start_time
            avg_time = elapsed / i
            etc = avg_time * (n_units - i)
            etc_min, etc_sec = divmod(int(etc), 60)
            percent = (i / n_units) * 100
            print(f"\r[{i:3}/{n_units}] {percent:6.2f}% | Unit {u} | ETC: {etc_min:02}:{etc_sec:02}", end='', flush=True)

        except Exception as e:
            print(f"\n⚠️  Error with unit {u}: {e}")
            continue

    print("\n✅ Done.")
    df = pd.DataFrame(data_units)

    # Optional save
    if savepath is not None:
        os.makedirs(os.path.dirname(savepath), exist_ok=True)
        df.to_pickle(savepath)
        print(f"📁 Saved to: {savepath}")

    return df


import os
import glob
import numpy as np
import spikeinterface.extractors as se
import spikeinterface.preprocessing as spre

def extract_analog_data(datapath, real_channel_indices, labels, target_fs=30000, savepath=None):
    """
    Load analog data from a folder, resample if needed, and return traces and labels only for specified real channels.
    Optionally save each channel's timeseries to .npy files named by their labels.

    Parameters:
        datapath (str): Folder path containing .bin and .meta files.
        real_channel_indices (list or array): Indices of channels with real data to keep.
        labels (list or array): Labels corresponding to the real channels.
        target_fs (float): Desired sampling frequency after resampling (default 30000 Hz).
        savepath (str or None): Optional folder path to save individual channel .npy files.

    Returns:
        traces (np.ndarray): 2D array of shape (num_samples, num_real_channels) with signal traces.
        labels (np.ndarray): Array of labels corresponding to the real channels.
        fs (float): Original sampling frequency from the .meta file.
        time_vector (np.ndarray): 1D array of time values in seconds (length = num_samples).
    """
    # Find .meta file in folder
    meta_files = glob.glob(os.path.join(datapath, "*.meta"))
    if not meta_files:
        raise FileNotFoundError(f"No .meta file found in folder {datapath}")
    meta_file = meta_files[0]

    # Find .bin file in folder
    bin_files = glob.glob(os.path.join(datapath, "*.bin"))
    if not bin_files:
        raise FileNotFoundError(f"No .bin file found in folder {datapath}")
    bin_file = bin_files[0]

    # Read .meta to get sampling rate and number of saved channels
    sampling_rate = None
    num_channels = None
    with open(meta_file, 'r') as f:
        for line in f:
            if line.startswith('obSampRate='):
                sampling_rate = float(line.split('=')[1].strip())
            elif line.startswith('nSavedChans='):
                num_channels = int(line.split('=')[1].strip())

    if sampling_rate is None:
        raise ValueError(f"Sampling rate (obSampRate) not found in {meta_file}")
    if num_channels is None:
        raise ValueError(f"Number of channels (nSavedChans) not found in {meta_file}")

    # Validate real_channel_indices and labels
    real_channel_indices = np.array(real_channel_indices)
    labels = np.array(labels)
    if len(real_channel_indices) != len(labels):
        raise ValueError("Length of real_channel_indices and labels must match")
    if np.any(real_channel_indices >= num_channels) or np.any(real_channel_indices < 0):
        raise ValueError("Some real_channel_indices are out of valid channel range")

    # Load recording
    recording = se.BinaryRecordingExtractor(
        file_paths=[bin_file],
        sampling_frequency=sampling_rate,
        num_channels=num_channels,
        dtype='int16',
        time_axis=0
    )

    # Resample if needed
    if sampling_rate != target_fs:
        recording = spre.resample(recording, resample_rate=target_fs)
        effective_fs = target_fs
    else:
        effective_fs = sampling_rate

    # Extract only real channels
    recording_real = recording.channel_slice(channel_ids=real_channel_indices.tolist())

    # Get traces as numpy array: shape (num_samples, num_real_channels)
    traces = recording_real.get_traces().T  # (samples, channels)

    # Create time vector
    num_samples = traces.shape[0]
    time_vector = np.arange(num_samples) / effective_fs

    # Save each channel timeseries as .npy file named by label
    if savepath is not None:
        os.makedirs(savepath, exist_ok=True)
        for i, label in enumerate(labels):
            filename = f"{label}.npy"
            filepath = os.path.join(savepath, filename)
            np.save(filepath, traces[:, i])

    return traces, labels, effective_fs, time_vector

import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
from datetime import datetime
import re

def _generate_plot_filename(description: str, extension: str = 'png', plots_dir: Optional[Path | str] = None, date: Optional[str] = None) -> Path:
    """Generate a filename for plots in the format YYYYMMDD_description.ext.

    Parameters
    ----------
    description : str
        Description for the plot filename.
    extension : str, default 'png'
        File extension for the plot.
    plots_dir : Optional[Path | str], default None
        Directory to save plots. If None, uses 'plots' directory.
    date : Optional[str], default None
        Date string in YYYYMMDD format. If None, uses current date.

    Returns
    -------
    Path
        Full path to the plot file with standardized filename.
    """
    if date is None:
        date_str = datetime.now().strftime('%Y%m%d')
    else:
        date_str = str(date)

    # remove unwanted suffixes like '_trials_42' or '_39'
    cleaned_desc = re.sub(r'(_trials_\d+|_\d+)$', '', description)

    # sanitize for filesystem
    sanitized_desc = re.sub(r'[^\w\-.]', '_', cleaned_desc)
    sanitized_desc = re.sub(r'_+', '_', sanitized_desc).strip('_')

    if plots_dir is None:
        plots_dir = Path('plots')
    else:
        plots_dir = Path(plots_dir)
    plots_dir.mkdir(parents=True, exist_ok=True)

    counter = 0
    while True:
        if counter == 0:
            filename = f"{date_str}_{sanitized_desc}.{extension}"
        else:
            filename = f"{date_str}_{sanitized_desc}_{counter:03d}.{extension}"
        full_path = plots_dir / filename
        if not full_path.exists():
            return full_path
        counter += 1

def plot_firing_rate_and_analog(df, analog_trace, time_axis, savepath=None,
                                bin_size=2, duration_sec=None,
                                analog_label="Balloon pressure (a.u.)",
                                use_standard_filenames=True, date=None):
    """
    Plot a z-scored firing rate heatmap (from spike times) with analog trace below.

    Parameters:
        df (pd.DataFrame): DataFrame with spike_times_sec and depth for each unit.
        analog_trace (np.ndarray): 1D array of raw analog signal.
        time_axis (np.ndarray): 1D array of time values in seconds.
        savepath (str or None): If provided, saves figure to this path.
            If use_standard_filenames=True, this is used as a description for the standardized filename.
        bin_size (float): Time bin size for spike rate in seconds.
        duration_sec (float or None): Recording duration. If None, inferred from spike times and analog.
        analog_label (str): Label for the analog trace y-axis.
        use_standard_filenames (bool): If True, uses standardized YYYYMMDD_description.png filenames
            and saves to /plots directory. If False, uses the provided savepath directly.
        date (str or None): Date string in YYYYMMDD format for filename. If None, uses current date.
    """
    if duration_sec is None:
        spike_dur = np.max([np.max(times) for times in df['spike_times_sec']])
        analog_dur = time_axis[-1]
        duration_sec = max(spike_dur, analog_dur)

    bins = np.arange(0, duration_sec + bin_size, bin_size)
    t_bin_centers = (bins[:-1] + bins[1:]) / 2

    # Compute z-scored firing rates
    fr_z = []
    for spike_times in df['spike_times_sec']:
        counts, _ = np.histogram(spike_times, bins=bins)
        fr = counts / bin_size
        z = (fr - np.mean(fr)) / np.std(fr) if np.std(fr) > 0 else fr * 0
        fr_z.append(z)
    fr_z = np.array(fr_z)

    # Plotting
    plt.rcParams.update({
        'font.size': 12,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'axes.spines.left': True,
        'axes.spines.bottom': True
    })

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 6), sharex=True,
                                   gridspec_kw={'height_ratios': [2, 1]})

    vlim = 3
    im = ax1.imshow(fr_z, aspect='auto', cmap='coolwarm',
                    extent=[t_bin_centers[0], t_bin_centers[-1], 0, len(df)],
                    origin='lower', vmin=-vlim, vmax=vlim)

    first_depth = df['depth'].iloc[0]
    last_depth = df['depth'].iloc[-1]
    ax1.set_yticks([0, len(df) - 1])
    ax1.set_yticklabels([f"{first_depth} µm", f"{last_depth} µm"])
    ax1.set_ylabel("Depth (from bottom, µm)")
    ax1.set_xlim([0, duration_sec])

    ax2.plot(time_axis, analog_trace, color='black', linewidth=0.3)
    ax2.set_ylabel(analog_label)
    ax2.set_xlabel("Time (s)")
    ax2.set_xlim([0, duration_sec])
    ax2.set_ylim(np.percentile(analog_trace, [0.1, 99.9]))

    plt.tight_layout()
    plt.subplots_adjust(hspace=0.15)

    if savepath is not None:
        if use_standard_filenames:
            # Use savepath as description for standardized filename
            description = str(savepath).replace('.png', '').replace('.pdf', '').replace('.jpg', '')
            description = f"firing_rate_and_analog_{description}"
            final_path = _generate_plot_filename(description, extension='png', date=date)
        else:
            # Use provided path directly (legacy behavior)
            final_path = savepath

        plt.savefig(final_path, dpi=300)
        print(f"📊 Plot saved to: {final_path}")

    plt.show()

import scipy.io
import pandas as pd
import numpy as np
import os

def extract_probe_trajectory(filepath, output_path=None):
    """
    Extracts trajectory region information from a .mat file and saves a cleaned DataFrame.

    Parameters
    ----------
    filepath : str
        Path to the 'probe_ccf_struct.mat' file.
    output_path : str or None
        Optional path to save the output CSV file. If None, saves next to input file.

    Returns
    -------
    df_trajectory : pandas.DataFrame
        DataFrame with columns: 'region_name', 'region_acronym', 'depth_from', 'depth_to'.
    """
    mat = scipy.io.loadmat(filepath, struct_as_record=False, squeeze_me=True)

    # Try to find 'probe' variable (structure array)
    probe_struct = None
    if 'probe' in mat:
        probe_struct = mat['probe']
    else:
        # Fallback: grab the first large struct-like array
        for key, val in mat.items():
            if isinstance(val, np.ndarray) and val.dtype.names and 'trajectory_depth' in val.dtype.names:
                probe_struct = val
                break
        if probe_struct is None:
            raise KeyError("No suitable 'probe' structure found in the .mat file.")

    rows = []
    for entry in probe_struct:
        try:
            region_name = str(entry.safe_name)
            region_acronym = str(entry.acronym)
            traj_depth = entry.trajectory_depth

            if isinstance(traj_depth, (list, np.ndarray)) and len(traj_depth) > 1:
                depth_from = int(traj_depth[0])
                depth_to = int(traj_depth[1])
            else:
                depth_from, depth_to = None, None

            rows.append({
                'region_name': region_name,
                'region_acronym': region_acronym,
                'depth_from': depth_from,
                'depth_to': depth_to
            })
        except Exception as e:
            print(f"Skipping entry due to error: {e}")
            continue

    df_trajectory = pd.DataFrame(rows)

    # Save CSV
    if output_path is None:
        output_path = os.path.splitext(filepath)[0] + '_trajectory.csv'
    df_trajectory.to_csv(output_path, index=False)

    return df_trajectory

